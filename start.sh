#!/bin/bash
# Startup script for LangGraph FastAPI application

# Don't exit on error, we want to handle errors gracefully
set +e

# Print Python and environment information
echo "Starting LangGraph FastAPI application"
echo "Python version: $(python --version)"
echo "Environment: $APP_ENV"

# Check for required environment variables
required_vars=(
  "OPENAI_API_KEY"
  "PINECONE_API_KEY"
  "PINECONE_ENVIRONMENT"
  "PINECONE_INDEX_NAME"
  "SUPABASE_URL"
  "SUPABASE_KEY"
  "CPK_ENDPOINT_SECRET"
)

missing_vars=()
for var in "${required_vars[@]}"; do
  if [ -z "${!var}" ]; then
    missing_vars+=("$var")
  fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
  echo "WARNING: Missing required environment variables:"
  for var in "${missing_vars[@]}"; do
    echo "  - $var"
  done
  echo "Please set these variables in the Fly.io dashboard or environment."
  echo "Setting up mock environment for testing purposes."

  # Set up mock environment variables for testing
  if [[ " ${missing_vars[@]} " =~ " OPENAI_API_KEY " ]]; then
    export OPENAI_API_KEY="sk-mock-key-for-testing"
    echo "Using mock OPENAI_API_KEY"
  fi

  if [[ " ${missing_vars[@]} " =~ " PINECONE_API_KEY " ]]; then
    export PINECONE_API_KEY="mock-pinecone-key-for-testing"
    echo "Using mock PINECONE_API_KEY"
  fi

  if [[ " ${missing_vars[@]} " =~ " PINECONE_ENVIRONMENT " ]]; then
    export PINECONE_ENVIRONMENT="us-east-1"
    echo "Using mock PINECONE_ENVIRONMENT"
  fi

  if [[ " ${missing_vars[@]} " =~ " PINECONE_INDEX_NAME " ]]; then
    export PINECONE_INDEX_NAME="mock-index"
    echo "Using mock PINECONE_INDEX_NAME"
  fi

  if [[ " ${missing_vars[@]} " =~ " SUPABASE_URL " ]]; then
    export SUPABASE_URL="https://mock-supabase-url.supabase.co"
    echo "Using mock SUPABASE_URL"
  fi

  if [[ " ${missing_vars[@]} " =~ " SUPABASE_KEY " ]]; then
    export SUPABASE_KEY="mock-supabase-key"
    echo "Using mock SUPABASE_KEY"
  fi

  if [[ " ${missing_vars[@]} " =~ " CPK_ENDPOINT_SECRET " ]]; then
    export CPK_ENDPOINT_SECRET="mock-endpoint-secret"
    echo "Using mock CPK_ENDPOINT_SECRET"
  fi

  if [[ " ${missing_vars[@]} " =~ " VOYAGE_API_KEY " ]]; then
    export VOYAGE_API_KEY="mock-voyage-key"
    echo "Using mock VOYAGE_API_KEY"
  fi

  echo "Mock environment variables set up for testing."
  echo "NOTE: Some functionality will be limited or unavailable."
fi

# Create log directory if it doesn't exist
mkdir -p /app/logs

# Run database migrations if needed
if [ -f "alembic.ini" ]; then
  echo "Running database migrations..."
  python -m alembic upgrade head
else
  echo "No alembic.ini found, skipping migrations."
fi

# Create a wrapper script to handle errors gracefully
cat > /tmp/run_app.py << EOF
import os
import sys
import uvicorn
import importlib.util
import traceback

def run_app():
    try:
        # Try to import the app
        print("Importing app module...")
        spec = importlib.util.spec_from_file_location("app", "/app/src/pi_lawyer/api/runtime.py")
        app_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(app_module)

        # Start the server
        print("Starting uvicorn server...")
        uvicorn.run(
            "pi_lawyer.api.runtime:app",
            host="0.0.0.0",
            port=int(os.environ.get("PORT", 8000)),
            log_level=os.environ.get("LOG_LEVEL", "info").lower()
        )
    except Exception as e:
        print(f"Error starting application: {e}")
        print("Traceback:")
        traceback.print_exc()

        print("\nStarting minimal health check server instead...")

        # Create a minimal FastAPI app for health checks
        from fastapi import FastAPI
        import datetime

        minimal_app = FastAPI(title="LangGraph API (Minimal Mode)")

        @minimal_app.get("/health")
        async def health_check():
            return {
                "status": "limited",
                "message": "Running in minimal mode due to configuration errors",
                "error": str(e),
                "timestamp": datetime.datetime.utcnow().isoformat(),
                "environment": os.environ.get("APP_ENV", "development")
            }

        uvicorn.run(minimal_app, host="0.0.0.0", port=int(os.environ.get("PORT", 8000)))

if __name__ == "__main__":
    run_app()
EOF

# Start the application with the wrapper script
echo "Starting FastAPI server..."
python /tmp/run_app.py
