"""
Unit tests for the Multimodal Document Processor.

This module contains comprehensive tests for the unified multimodal document processor,
including tests for both legal and medical document processing domains.
"""

import json
import os
import tempfile
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from typing import List

import pytest
from langchain_core.documents import Document

from backend.agents.interactive.research.multimodal_processor import (
    MultimodalDocumentProcessor,
    process_multimodal_document
)


class TestMultimodalDocumentProcessor:
    """Test suite for MultimodalDocumentProcessor class."""

    @pytest.fixture
    def processor(self):
        """Create a processor instance for testing."""
        with patch.dict(os.environ, {"GOOGLE_API_KEY": "test-key"}):
            with patch("google.generativeai.configure"):
                with patch("google.generativeai.GenerativeModel") as mock_model:
                    mock_model.return_value = MagicMock()
                    return MultimodalDocumentProcessor()

    @pytest.fixture
    def mock_pdf_path(self):
        """Create a temporary PDF file path for testing."""
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp:
            tmp.write(b"Mock PDF content")
            yield tmp.name
        os.unlink(tmp.name)

    @pytest.fixture
    def mock_unstructured_elements(self):
        """Mock Unstructured elements for testing."""
        class MockElement:
            def __init__(self, content, element_type="Text"):
                self.content = content
                self.element_type = element_type
                self.metadata = {"page": 1}
            
            def __str__(self):
                return self.content

        return [
            MockElement("This is a legal contract clause about liability.", "Text"),
            MockElement("Table: Billing rates and procedures", "Table"),
            MockElement("Medical diagnosis: Patient shows signs of...", "Text")
        ]

    @patch("backend.agents.interactive.research.multimodal_processor.partition_pdf")
    @patch("backend.agents.interactive.research.multimodal_processor.UNSTRUCTURED_AVAILABLE", True)
    async def test_parse_with_unstructured(self, mock_partition, processor, mock_pdf_path, mock_unstructured_elements):
        """Test document parsing with Unstructured."""
        mock_partition.return_value = mock_unstructured_elements

        elements = await processor._parse_with_unstructured(mock_pdf_path)
        
        assert len(elements) == 3
        assert str(elements[0]) == "This is a legal contract clause about liability."
        mock_partition.assert_called_once_with(
            filename=mock_pdf_path,
            strategy="hi_res",
            infer_table_structure=True,
            extract_images_in_pdf=True,
            extract_image_block_types=["Image", "Table"],
            chunking_strategy="by_title",
            max_characters=1000,
            combine_text_under_n_chars=100
        )

    async def test_analyze_legal_element(self, processor):
        """Test legal element analysis with Gemini."""
        mock_element = MagicMock()
        mock_element.__str__ = MagicMock(return_value="Contract clause about indemnification")
        
        # Mock Gemini response
        mock_response = MagicMock()
        mock_response.text = json.dumps({
            "summary": "Indemnification clause analysis",
            "key_points": ["Liability protection", "Third-party claims"],
            "risks": ["Broad indemnification scope"],
            "texas_relevance": "Complies with Texas Civil Practice Code",
            "structured_data": {},
            "confidence": 0.8,
            "requires_review": False
        })
        
        processor.gemini_model.generate_content_async = AsyncMock(return_value=mock_response)
        
        analysis = await processor._analyze_legal_element(mock_element)
        
        assert analysis["summary"] == "Indemnification clause analysis"
        assert analysis["confidence"] == 0.8
        assert analysis["requires_review"] is False
        assert "Texas Civil Practice Code" in analysis["texas_relevance"]

    async def test_analyze_medical_element_with_gemini(self, processor):
        """Test medical element analysis with Gemini 2.5 Pro."""
        mock_element = MagicMock()
        mock_element.__str__ = MagicMock(return_value="Patient presents with acute chest pain")

        # Mock Gemini response
        mock_response = MagicMock()
        mock_response.text = json.dumps({
            "diagnoses": ["Acute chest pain", "Rule out MI"],
            "clinical_summary": "Patient requires immediate cardiac evaluation",
            "fhir_data": {"condition": "chest_pain"},
            "legal_relevance": "Potential delay in diagnosis concern",
            "causation_links": ["Symptom onset timing"],
            "standard_of_care": "Appropriate triage and evaluation",
            "hipaa_flags": ["patient_data"],
            "confidence": 0.6,
            "requires_review": False
        })
        processor.gemini_model.generate_content_async = AsyncMock(return_value=mock_response)

        analysis = await processor._analyze_medical_element(mock_element)

        assert "Acute chest pain" in analysis["diagnoses"]
        assert analysis["confidence"] == 0.6
        assert "cardiac evaluation" in analysis["clinical_summary"]
        assert "hipaa_flags" in analysis

    async def test_analyze_medical_element_json_fallback(self, processor):
        """Test medical element analysis with JSON parsing fallback."""
        mock_element = MagicMock()
        mock_element.__str__ = MagicMock(return_value="EKG shows ST elevation")

        # Mock Gemini response with invalid JSON
        
        # Mock Gemini response with invalid JSON
        mock_response = MagicMock()
        mock_response.text = "Invalid JSON response about diabetes"
        processor.gemini_model.generate_content_async = AsyncMock(return_value=mock_response)

        analysis = await processor._analyze_medical_element(mock_element)

        assert analysis["clinical_summary"] == "Invalid JSON response about diabetes"[:500]
        assert analysis["confidence"] == 0.6  # Fallback confidence for Gemini
        assert analysis["requires_review"] == True
        assert "hipaa_flags" in analysis

    def test_create_enriched_document_legal(self, processor):
        """Test creation of enriched legal document."""
        mock_element = MagicMock()
        mock_element.__str__ = MagicMock(return_value="Legal contract text")
        
        analysis = {
            "summary": "Contract analysis",
            "key_points": ["Payment terms", "Termination clause"],
            "risks": ["Penalty provisions"],
            "texas_relevance": "Texas Business Code compliant",
            "structured_data": {"payment_due": "30 days"},
            "confidence": 0.7,
            "requires_review": False
        }
        
        doc = processor._create_enriched_document(mock_element, analysis, "legal")
        
        assert isinstance(doc, Document)
        assert doc.page_content == "Legal contract text"
        assert doc.metadata["domain"] == "legal"
        assert doc.metadata["confidence"] == 0.7
        assert doc.metadata["legal_summary"] == "Contract analysis"
        assert "Payment terms" in doc.metadata["key_points"]

    def test_create_enriched_document_medical(self, processor):
        """Test creation of enriched medical document."""
        mock_element = MagicMock()
        mock_element.__str__ = MagicMock(return_value="Medical record entry")
        
        analysis = {
            "diagnoses": ["Hypertension", "Diabetes"],
            "clinical_summary": "Patient with multiple comorbidities",
            "fhir_data": {"condition": "hypertension"},
            "legal_relevance": "Pre-existing conditions noted",
            "causation_links": ["Medication compliance"],
            "standard_of_care": "Appropriate management",
            "confidence": 0.85,
            "requires_review": False
        }
        
        doc = processor._create_enriched_document(mock_element, analysis, "medical")
        
        assert isinstance(doc, Document)
        assert doc.page_content == "Medical record entry"
        assert doc.metadata["domain"] == "medical"
        assert doc.metadata["confidence"] == 0.85
        assert "Hypertension" in doc.metadata["diagnoses"]
        assert doc.metadata["clinical_summary"] == "Patient with multiple comorbidities"

    def test_create_enriched_document_low_confidence(self, processor):
        """Test low confidence flagging in enriched documents."""
        mock_element = MagicMock()
        mock_element.__str__ = MagicMock(return_value="Unclear text")
        
        analysis = {
            "summary": "Uncertain analysis",
            "confidence": 0.3,  # Low confidence
            "requires_review": True
        }
        
        doc = processor._create_enriched_document(mock_element, analysis, "legal")
        
        assert doc.metadata["low_confidence"] is True
        assert doc.metadata["requires_review"] is True

    def test_create_fallback_document(self, processor):
        """Test fallback document creation on processing errors."""
        mock_element = MagicMock()
        mock_element.__str__ = MagicMock(return_value="Failed element")
        
        doc = processor._create_fallback_document(mock_element, "legal", "Processing error")
        
        assert isinstance(doc, Document)
        assert doc.page_content == "Failed element"
        assert doc.metadata["domain"] == "legal"
        assert doc.metadata["processing_error"] == "Processing error"
        assert doc.metadata["confidence"] == 0.0
        assert doc.metadata["requires_review"] is True
        assert doc.metadata["fallback"] is True

    @patch("backend.agents.interactive.research.multimodal_processor.partition_pdf")
    async def test_process_multimodal_document_legal(self, mock_partition, processor, mock_pdf_path, mock_unstructured_elements):
        """Test complete legal document processing workflow."""
        mock_partition.return_value = mock_unstructured_elements[:1]  # Just one element
        
        # Mock Gemini response
        mock_response = MagicMock()
        mock_response.text = json.dumps({
            "summary": "Legal analysis complete",
            "key_points": ["Key point 1"],
            "risks": ["Risk 1"],
            "texas_relevance": "Texas compliant",
            "structured_data": {},
            "confidence": 0.8,
            "requires_review": False
        })
        
        processor.gemini_model.generate_content_async = AsyncMock(return_value=mock_response)
        
        documents = await processor.process_multimodal_document(mock_pdf_path, "pdf", "legal")
        
        assert len(documents) == 1
        assert documents[0].metadata["domain"] == "legal"
        assert documents[0].metadata["confidence"] == 0.8

    @patch("backend.agents.interactive.research.multimodal_processor.partition_pdf")
    async def test_process_multimodal_document_medical(self, mock_partition, processor, mock_pdf_path, mock_unstructured_elements):
        """Test complete medical document processing workflow."""
        mock_partition.return_value = mock_unstructured_elements[2:]  # Medical element
        
        # Mock Gemini response for medical analysis
        mock_response = MagicMock()
        mock_response.text = json.dumps({
            "diagnoses": ["Test diagnosis"],
            "clinical_summary": "Medical summary",
            "fhir_data": {},
            "legal_relevance": "Legal relevance",
            "causation_links": [],
            "standard_of_care": "Standard care",
            "hipaa_flags": ["patient_data"],
            "confidence": 0.6,
            "requires_review": False
        })
        processor.gemini_model.generate_content_async = AsyncMock(return_value=mock_response)
        
        documents = await processor.process_multimodal_document(mock_pdf_path, "pdf", "medical")
        
        assert len(documents) == 1
        assert documents[0].metadata["domain"] == "medical"
        assert documents[0].metadata["confidence"] == 0.6
        assert "Test diagnosis" in documents[0].metadata["diagnoses"]


class TestConvenienceFunction:
    """Test suite for the convenience function."""

    @patch("backend.agents.interactive.research.multimodal_processor.MultimodalDocumentProcessor")
    async def test_process_multimodal_document_function(self, mock_processor_class):
        """Test the convenience function creates processor and calls method."""
        mock_processor = MagicMock()
        mock_processor.process_multimodal_document = AsyncMock(return_value=[])
        mock_processor_class.return_value = mock_processor
        
        result = await process_multimodal_document("/test/path.pdf", "pdf", "legal")
        
        mock_processor_class.assert_called_once()
        mock_processor.process_multimodal_document.assert_called_once_with("/test/path.pdf", "pdf", "legal")
        assert result == []
