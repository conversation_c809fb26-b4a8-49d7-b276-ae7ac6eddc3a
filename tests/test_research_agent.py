"""
Unit tests for the Research Agent implementation.

This module contains tests for the Research Agent's state model,
node functions, and graph workflow.
"""

import json
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from backend.agents.interactive.research.graph import create_research_graph
from backend.agents.interactive.research.nodes import (
    classify_query,
    collect_citations,
    exit_guard,
    gpt_query_gen,
    graph_expand,
    handle_error,
    llm_answer,
    long_research_queue,
    process_case_documents,
    rerank,
    route_by_query_type,
    vector_search,
    web_citation_mapper,
    web_search,
)
from backend.agents.interactive.research.state import (
    Document,
    ResearchState,
    UserContext,
)


# Test fixtures
@pytest.fixture
def user_context():
    """Create a test user context."""
    return UserContext(
        user_id="test-user",
        tenant_id="test-tenant",
        role="attorney",
        assigned_case_ids=["case-1", "case-2"],
        settings={}
    )


@pytest.fixture
def research_state(user_context):
    """Create a test research state."""
    return ResearchState(
        question="What is the statute of limitations for personal injury in Texas?",
        user_context=user_context,
        jurisdiction="texas",
        practice_areas={"personal_injury"},
    )


@pytest.fixture
def mock_openai_response():
    """Create a mock OpenAI response."""
    mock_response = MagicMock()
    mock_response.choices = [MagicMock()]
    mock_response.choices[0].message = MagicMock()
    mock_response.choices[0].message.content = json.dumps({
        "query_type": "legal_research",
        "data_source": "public",
        "references_case": False,
        "contains_sensitive_info": False,
        "confidence": 0.95,
        "reasoning": "This is a general legal question about Texas law."
    })
    return mock_response


@pytest.fixture
def mock_openai_client(mock_openai_response):
    """Create a mock OpenAI client."""
    mock_client = MagicMock()
    mock_client.chat = MagicMock()
    mock_client.chat.completions = MagicMock()
    mock_client.chat.completions.create = AsyncMock(return_value=mock_openai_response)
    return mock_client


# Tests for state model
def test_research_state_initialization(user_context):
    """Test that ResearchState initializes correctly with required fields."""
    state = ResearchState(
        question="Test question",
        user_context=user_context
    )

    assert state.question == "Test question"
    assert state.user_context == user_context
    assert state.jurisdiction == "texas"  # Default value
    assert state.practice_areas == set()  # Default empty set
    assert state.queries == []  # Default empty list
    assert state.query_type is None
    assert state.data_source is None
    assert state.legal_documents == []
    assert state.case_documents == []
    assert state.web_search_results == []
    assert state.citations == []
    assert state.citation_map == {}
    assert state.rerank_scores == []
    assert state.search_metadata == {}
    assert state.answer is None


def test_research_state_can_view_results(user_context):
    """Test the can_view_results method of ResearchState."""
    # Public data is always accessible
    state = ResearchState(
        question="Test question",
        user_context=user_context,
        data_source="public"
    )
    assert state.can_view_results() is True

    # Private data is accessible to attorneys
    state = ResearchState(
        question="Test question",
        user_context=user_context,
        data_source="private"
    )
    assert state.can_view_results() is True

    # Private data is not accessible to paralegals
    paralegal_context = UserContext(
        user_id="test-user",
        tenant_id="test-tenant",
        role="paralegal",
        assigned_case_ids=["case-1", "case-2"],
        settings={}
    )
    state = ResearchState(
        question="Test question",
        user_context=paralegal_context,
        data_source="private"
    )
    assert state.can_view_results() is False

    # Case-specific access check
    state = ResearchState(
        question="Test question",
        user_context=user_context,
        data_source="private",
        case_id="case-1"
    )
    assert state.can_view_results() is True

    state = ResearchState(
        question="Test question",
        user_context=user_context,
        data_source="private",
        case_id="case-3"  # Not in assigned_case_ids
    )
    assert state.can_view_results() is False


# Tests for node functions
@patch("backend.agents.interactive.research.nodes.get_llm_client")
@patch("backend.agents.interactive.research.nodes.generate_completion")
async def test_classify_query(mock_generate_completion, mock_get_openai, mock_openai_client, research_state):
    """Test the classify_query node function."""
    mock_get_openai.return_value = mock_openai_client

    # Mock the generate_completion function to return a valid response
    mock_generate_completion.return_value = MagicMock(
        content=json.dumps({
            "query_type": "legal_research",
            "data_source": "public",
            "references_case": False,
            "contains_sensitive_info": False,
            "confidence": 0.95,
            "reasoning": "This is a general legal question about Texas law."
        })
    )

    result = await classify_query(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "route_query"
    assert research_state.query_type == "legal_research"
    assert research_state.data_source == "public"
    assert research_state.inferred_mode == "public"  # For backward compatibility
    assert "classification" in research_state.search_metadata
    assert research_state.search_metadata["classification"]["confidence"] == 0.95


async def test_route_by_query_type(research_state):
    """Test the route_by_query_type node function."""
    # Test legal_research route
    research_state.query_type = "legal_research"
    result = await route_by_query_type(research_state, {})
    assert result["status"] == "success"
    assert result["next"] == "gpt_query_gen"

    # Test web_search route
    research_state.query_type = "web_search"
    result = await route_by_query_type(research_state, {})
    assert result["status"] == "success"
    assert result["next"] == "web_search"

    # Test deep_research route
    research_state.query_type = "deep_research"
    result = await route_by_query_type(research_state, {})
    assert result["status"] == "success"
    assert result["next"] == "long_research_queue"

    # Test non_legal route
    research_state.query_type = "non_legal"
    result = await route_by_query_type(research_state, {})
    assert result["status"] == "success"
    assert result["next"] == "web_search"

    # Test default route
    research_state.query_type = None
    result = await route_by_query_type(research_state, {})
    assert result["status"] == "success"
    assert result["next"] == "gpt_query_gen"


@patch("backend.agents.interactive.research.nodes.get_llm_client")
@patch("backend.agents.interactive.research.nodes.generate_completion")
async def test_gpt_query_gen(mock_generate_completion, mock_get_openai, mock_openai_client, research_state):
    """Test the gpt_query_gen node function."""
    mock_get_openai.return_value = mock_openai_client

    # Mock the generate_completion function to return a valid response
    mock_generate_completion.return_value = MagicMock(
        content=json.dumps({
            "vector_queries": [
                "statute of limitations personal injury Texas",
                "Texas personal injury time limit file lawsuit"
            ],
            "keyword_query": "Texas personal injury statute of limitations"
        })
    )

    result = await gpt_query_gen(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "vector_search"
    assert len(research_state.queries) == 2
    assert research_state.keyword_query == "Texas personal injury statute of limitations"


async def test_vector_search(research_state):
    """Test the vector_search node function."""
    # Set up state for search
    research_state.queries = ["statute of limitations personal injury Texas"]

    # Directly set the legal_documents in the state
    research_state.legal_documents = [
        Document(
            page_content="The statute of limitations for personal injury in Texas is two years.",
            metadata={
                "id": "doc-1",
                "title": "Texas Civil Practice and Remedies Code",
                "jurisdiction": "texas"
            }
        )
    ]

    # Import the actual function

    # Call the function
    result = await vector_search(research_state, {})

    # Verify the results
    assert result["status"] == "success"
    assert result["next"] == "graph_expand"
    assert len(research_state.legal_documents) == 1
    assert research_state.legal_documents[0].page_content == "The statute of limitations for personal injury in Texas is two years."


async def test_graph_expand(research_state):
    """Test the graph_expand node function."""
    # Set up state with legal documents
    research_state.legal_documents = [
        Document(
            page_content="The statute of limitations for personal injury in Texas is two years.",
            metadata={"id": "doc-1", "title": "Texas Civil Practice and Remedies Code"}
        )
    ]

    result = await graph_expand(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "rerank"


async def test_rerank(research_state):
    """Test the rerank node function."""
    # Set up state with legal documents
    research_state.legal_documents = [
        Document(
            page_content="The statute of limitations for personal injury in Texas is two years.",
            metadata={"id": "doc-1", "title": "Texas Civil Practice and Remedies Code"}
        ),
        Document(
            page_content="Medical malpractice claims in Texas have specific requirements.",
            metadata={"id": "doc-2", "title": "Texas Medical Liability Act"}
        )
    ]

    result = await rerank(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "collect_citations"
    assert len(research_state.rerank_scores) == 2


async def test_web_search(research_state):
    """Test the web_search node function."""
    result = await web_search(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "web_citation_mapper"
    assert len(research_state.web_search_results) == 5  # Mock returns 5 results


async def test_web_citation_mapper(research_state):
    """Test the web_citation_mapper node function."""
    # Set up state with web search results
    research_state.web_search_results = [
        {
            "id": "web-1",
            "title": "Texas Statute of Limitations Guide",
            "url": "https://example.com/texas-statutes",
            "snippet": "In Texas, the statute of limitations for personal injury is two years."
        }
    ]

    result = await web_citation_mapper(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "llm_answer"
    assert len(research_state.citations) == 1
    assert research_state.citations[0]["source_type"] == "web"
    assert 1 in research_state.citation_map


async def test_collect_citations(research_state):
    """Test the collect_citations node function."""
    # Set up state with legal documents
    research_state.legal_documents = [
        Document(
            page_content="The statute of limitations for personal injury in Texas is two years.",
            metadata={"id": "doc-1", "title": "Texas Civil Practice and Remedies Code"}
        )
    ]

    result = await collect_citations(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "llm_answer"
    assert len(research_state.citations) == 1
    assert research_state.citations[0]["source_type"] == "legal"
    assert 1 in research_state.citation_map


@patch("backend.agents.interactive.research.nodes.get_llm_client")
async def test_llm_answer(mock_get_openai, mock_openai_client, research_state):
    """Test the llm_answer node function."""
    mock_get_openai.return_value = mock_openai_client

    # Mock the response for answer generation
    mock_openai_client.chat.completions.create.return_value.choices[0].message.content = (
        "In Texas, the statute of limitations for personal injury claims is two years from the date of the injury. $1$"
    )

    # Set up state with legal documents and citations
    research_state.legal_documents = [
        Document(
            page_content="The statute of limitations for personal injury in Texas is two years.",
            metadata={"id": "doc-1", "title": "Texas Civil Practice and Remedies Code"}
        )
    ]
    research_state.citations = [
        {
            "id": "doc-1",
            "title": "Texas Civil Practice and Remedies Code",
            "source_type": "legal",
            "jurisdiction": "texas"
        }
    ]
    research_state.citation_map = {1: research_state.citations[0]}

    result = await llm_answer(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "exit_guard"
    assert research_state.answer is not None
    assert "$1$" in research_state.answer  # Check for citation marker


async def test_exit_guard(research_state):
    """Test the exit_guard node function."""
    # Set up state with answer
    research_state.answer = "In Texas, the statute of limitations for personal injury claims is two years from the date of the injury. $1$"
    research_state.query_type = "legal_research"
    research_state.citations = [
        {
            "id": "doc-1",
            "title": "Texas Civil Practice and Remedies Code",
            "source_type": "legal",
            "jurisdiction": "texas"
        }
    ]

    result = await exit_guard(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "FINISH"
    assert "result" in research_state.search_metadata


async def test_long_research_queue(research_state):
    """Test the long_research_queue node function."""
    result = await long_research_queue(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "FINISH"
    assert "long_research_task_id" in research_state.search_metadata
    assert research_state.answer is not None


async def test_handle_error(research_state):
    """Test the handle_error node function."""
    # Set up state with error
    research_state.search_metadata["error"] = "Test error"

    result = await handle_error(research_state, {})

    assert result["status"] == "error"
    assert result["next"] == "FINISH"
    assert research_state.answer is not None


@patch("backend.agents.interactive.research.nodes.process_multimodal_document")
async def test_process_case_documents(mock_process_multimodal, research_state):
    """Test the process_case_documents node function."""
    # Set up state with case_id and tenant_id
    research_state.case_id = "case-123"
    research_state.user_context.tenant_id = "tenant-456"

    # Mock multimodal processor response
    mock_documents = [
        Document(
            page_content="Legal contract clause about liability",
            metadata={
                "domain": "legal",
                "confidence": 0.8,
                "requires_review": False,
                "legal_summary": "Liability clause analysis"
            }
        ),
        Document(
            page_content="Medical diagnosis: Patient shows signs of injury",
            metadata={
                "domain": "medical",
                "confidence": 0.9,
                "requires_review": False,
                "diagnoses": ["Injury", "Trauma"]
            }
        )
    ]
    mock_process_multimodal.return_value = mock_documents

    result = await process_case_documents(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "rerank"
    # Note: In the current implementation, case_documents would be empty
    # because we don't have actual document paths. This test validates
    # the node structure and error handling.


async def test_process_case_documents_no_case_id(research_state):
    """Test process_case_documents with no case_id."""
    # Don't set case_id
    research_state.case_id = None

    result = await process_case_documents(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "rerank"
    assert len(research_state.case_documents) == 0


async def test_process_case_documents_no_tenant_id(research_state):
    """Test process_case_documents with no tenant_id."""
    research_state.case_id = "case-123"
    research_state.user_context.tenant_id = None

    result = await process_case_documents(research_state, {})

    assert result["status"] == "success"
    assert result["next"] == "rerank"
    assert len(research_state.case_documents) == 0


# Test the complete graph
@patch("backend.agents.interactive.research.nodes.get_llm_client")
async def test_research_graph(mock_get_openai, mock_openai_client, research_state):
    """Test the complete research graph workflow."""
    mock_get_openai.return_value = mock_openai_client

    # Mock the responses for different stages
    mock_openai_client.chat.completions.create.return_value.choices[0].message.content = json.dumps({
        "query_type": "legal_research",
        "data_source": "public",
        "references_case": False,
        "contains_sensitive_info": False,
        "confidence": 0.95,
        "reasoning": "This is a general legal question about Texas law."
    })

    # Set the query_type directly on the state
    research_state.query_type = "legal_research"
    research_state.data_source = "public"

    # Create the graph
    graph = create_research_graph()

    # Execute the graph
    with patch("backend.agents.interactive.research.nodes.vector_search", new=AsyncMock(return_value={"status": "success", "next": "graph_expand"})):
        with patch("backend.agents.interactive.research.nodes.graph_expand", new=AsyncMock(return_value={"status": "success", "next": "rerank"})):
            with patch("backend.agents.interactive.research.nodes.rerank", new=AsyncMock(return_value={"status": "success", "next": "collect_citations"})):
                with patch("backend.agents.interactive.research.nodes.collect_citations", new=AsyncMock(return_value={"status": "success", "next": "llm_answer"})):
                    with patch("backend.agents.interactive.research.nodes.llm_answer", new=AsyncMock(return_value={"status": "success", "next": "exit_guard"})):
                        with patch("backend.agents.interactive.research.nodes.exit_guard", new=AsyncMock(return_value={"status": "success", "next": "FINISH"})):
                            # Execute the graph and store the result
                            result_state = await graph.ainvoke(research_state)
                            # Verify the result state is returned
                            assert result_state is not None

    # Check that the graph executed successfully
    assert research_state.query_type == "legal_research"
    assert research_state.data_source == "public"
