# AiLex Unified Multimodal Document Processor

## Overview

The AiLex Unified Multimodal Document Processor is a production-ready module that combines Unstructured OSS for parsing with domain-specific AI analysis. It enhances the Research Agent by automating comprehensive document review across legal and medical domains, creating massive value for solo attorneys and small firms.

## Key Features

### Unified Processing Architecture
- **Unstructured OSS Integration**: High-quality document parsing with `hi_res` strategy
- **Domain-Specific AI Models**: 
  - Legal documents: Gemini 2.5 Pro for general reliability and Texas jurisdiction awareness
  - Medical documents: MedGemma 27B for specialized medical accuracy on EHRQA/FHIR
- **Multimodal Support**: Handles PDFs with text, tables, and images
- **Confidence Scoring**: Automatic flagging for human review when uncertainty > 0.5

### Business Value for Legal Professionals

#### Personal Injury (PI) and Medical Malpractice Cases
- Automates extraction of injury details from X-rays and histopathology reports
- Flags negligence patterns (e.g., "This MRI shows untreated fractures consistent with Texas tort claims")
- Saves 5-10 hours/week on manual cross-referencing
- Reduces overlooked evidence, boosting case win rates

#### Workers' Compensation and Disability Claims
- Parses medical reports for causation links (e.g., "EHR notes indicate workplace aggravation")
- Integrates with legal statutes for automated brief generation
- Cuts prep time by 40-60%
- Enables solo practitioners to handle more clients without paralegals

#### Health-Related Contracts and Compliance
- Analyzes tables in provider agreements and HIPAA documents
- Spots compliance risks (e.g., "This billing table violates CMS rules based on diagnosis codes")
- Automates compliance checks to prevent fines
- Adds billable value through proactive risk identification

#### General Litigation with Medical Evidence
- Processes health records in non-medical specialties (family/divorce law)
- Provides seamless insights (e.g., "Psych eval image suggests capacity issues for custody")
- Makes AiLex versatile for diverse caseloads
- Increases user retention and referrals

## Technical Architecture

### Core Components

#### MultimodalDocumentProcessor Class
```python
class MultimodalDocumentProcessor:
    """
    Unified multimodal document processor for legal and medical documents.
    
    Combines Unstructured OSS parsing with domain-specific AI analysis:
    - Legal documents: Gemini 2.5 Pro for general reliability
    - Medical documents: MedGemma 27B for specialized medical accuracy
    """
```

#### Key Methods
- `process_multimodal_document()`: Main processing interface
- `_parse_with_unstructured()`: Document parsing with Unstructured
- `_analyze_legal_element()`: Legal analysis with Gemini
- `_analyze_medical_element()`: Medical analysis with MedGemma
- `_create_enriched_document()`: LangChain Document creation with metadata

### Integration with Research Agent

#### New Research Node: `process_case_documents`
- Processes case-specific documents with multimodal analysis
- Integrates into Research Agent workflow between `vector_search` and `graph_expand`
- Supports both legal and medical document domains
- Adds case-specific metadata and confidence scoring

#### Workflow Integration
```
gpt_query_gen → vector_search → process_case_documents → graph_expand → rerank → collect_citations
```

### Configuration

#### Environment Variables
```bash
# Required for Gemini
GOOGLE_API_KEY=your_gemini_api_key
GEMINI_API_KEY=your_gemini_api_key  # Alternative

# Optional for MedGemma via Hugging Face
HF_TOKEN=your_hugging_face_token

# Optional for Vertex AI fallback
GOOGLE_CLOUD_PROJECT=your_project_id
```

#### Dependencies
```python
# Core dependencies
unstructured>=0.15.0        # Document parsing
transformers>=4.36.0        # Hugging Face models
torch>=2.0.0               # PyTorch for transformers
google-cloud-aiplatform>=1.38.0  # Vertex AI access
google-generativeai        # Gemini API
langchain-core             # Document structures
```

## Usage Examples

### Basic Usage
```python
from backend.agents.interactive.research.multimodal_processor import process_multimodal_document

# Process legal document
legal_docs = await process_multimodal_document(
    document_path="/path/to/contract.pdf",
    domain="legal"
)

# Process medical document  
medical_docs = await process_multimodal_document(
    document_path="/path/to/medical_report.pdf",
    domain="medical"
)

# Check confidence and review flags
for doc in legal_docs:
    if doc.metadata.get("low_confidence"):
        print(f"Document requires review: {doc.metadata['confidence']}")
    
    if doc.metadata.get("domain") == "legal":
        print(f"Legal summary: {doc.metadata['legal_summary']}")
        print(f"Texas relevance: {doc.metadata['texas_relevance']}")
    
    elif doc.metadata.get("domain") == "medical":
        print(f"Diagnoses: {doc.metadata['diagnoses']}")
        print(f"Legal relevance: {doc.metadata['legal_relevance']}")
```

### Research Agent Integration
The processor is automatically integrated into the Research Agent workflow when `case_id` is provided:

```python
# Research Agent will automatically process case documents
research_state = ResearchState(
    question="What are the liability implications of this medical malpractice case?",
    case_id="case-123",  # Triggers case document processing
    user_context=user_context
)

# The workflow will include multimodal processing of case documents
result = await research_agent.execute(research_state)
```

## Output Structure

### Legal Document Metadata
```python
{
    "domain": "legal",
    "element_type": "Text",
    "confidence": 0.8,
    "requires_review": False,
    "processing_model": "gemini-2.0-flash",
    "legal_summary": "Contract analysis with key terms",
    "key_points": ["Payment terms", "Termination clause"],
    "risks": ["Penalty provisions"],
    "texas_relevance": "Complies with Texas Business Code",
    "structured_data": {"payment_due": "30 days"}
}
```

### Medical Document Metadata
```python
{
    "domain": "medical",
    "element_type": "Text", 
    "confidence": 0.9,
    "requires_review": False,
    "processing_model": "medgemma-27b",
    "diagnoses": ["Hypertension", "Diabetes"],
    "clinical_summary": "Patient with multiple comorbidities",
    "fhir_data": {"condition": "hypertension"},
    "legal_relevance": "Pre-existing conditions for PI claim",
    "causation_links": ["Medication compliance"],
    "standard_of_care": "Appropriate management"
}
```

## Testing

### Unit Tests
Comprehensive test suite in `tests/test_multimodal_processor.py`:
- Document parsing with Unstructured
- Legal and medical element analysis
- Confidence scoring and review flagging
- Error handling and fallback scenarios
- Integration with Research Agent

### Research Agent Tests
Extended tests in `tests/test_research_agent.py`:
- Case document processing node
- Workflow integration
- Edge cases (no case_id, no tenant_id)

### Running Tests
```bash
# Run multimodal processor tests
pytest tests/test_multimodal_processor.py -v

# Run research agent tests including multimodal integration
pytest tests/test_research_agent.py::test_process_case_documents -v

# Run all tests
pytest tests/ -v
```

## Performance Considerations

### Optimization Strategies
- **Async Processing**: All document processing is asynchronous
- **Batch Processing**: Elements processed in batches for efficiency
- **Model Caching**: AI models are initialized once and reused
- **Fallback Mechanisms**: Graceful degradation when models unavailable

### Resource Management
- **Memory**: MedGemma 27B requires significant GPU memory
- **API Limits**: Gemini API rate limiting considerations
- **Processing Time**: Large documents may take several minutes

## Error Handling

### Confidence-Based Review
- Documents with confidence < 0.5 are flagged for human review
- `low_confidence` and `requires_review` metadata flags
- Fallback to text-only processing on analysis failures

### Graceful Degradation
- MedGemma unavailable → fallback to Gemini with medical context
- Unstructured parsing fails → error logging and empty result
- Individual element failures → continue processing remaining elements

## Future Enhancements

### Planned Features
- **Enhanced Medical Models**: Integration with specialized medical AI models
- **Custom Domain Training**: Fine-tuning for specific legal practice areas
- **Real-time Processing**: Streaming document analysis for large files
- **Advanced OCR**: Better handling of scanned documents and handwritten notes

### Integration Opportunities
- **Document Management**: Direct integration with case management systems
- **Workflow Automation**: Trigger document processing on file upload
- **Analytics Dashboard**: Confidence scoring and review metrics
- **Compliance Monitoring**: Automated HIPAA and legal compliance checking

## Support and Maintenance

### Monitoring
- Processing success/failure rates
- Confidence score distributions
- Model performance metrics
- User review feedback integration

### Updates
- Regular model updates for improved accuracy
- Unstructured library updates for better parsing
- Domain-specific prompt refinements
- Performance optimizations based on usage patterns
