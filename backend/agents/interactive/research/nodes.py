"""
AiLex Research Agent Node Implementations

This module contains the implementation of the nodes for the AiLex research agent.
Each node is a function that takes a state and returns an updated state or a command
to transition to another node.

The nodes implement the research workflow, including:
- Query classification and routing
- Query generation and optimization
- Vector search and graph expansion
- Web search (as an alternative path)
- Citation collection and formatting
- Answer generation with citations

These nodes are used to build the research agent graph in graph.py.
"""

import json
import logging
import os
import voyageai
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, cast

# Import multimodal processor
from .multimodal_processor import process_multimodal_document

# Neo4j GDS for community detection (optional import)
try:
    from neo4j import GraphDatabase
    NEO4J_AVAILABLE = True
except ImportError:
    NEO4J_AVAILABLE = False
    GraphDatabase = None


# Define RunnableConfig class for compatibility
@dataclass
class RunnableConfig:
    """Simple config class to replace langchain_core.runnables.RunnableConfig."""
    callbacks: Optional[List[Any]] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

from backend.agents.interactive.research.state import (
    Citation,
    DataSourceType,
    Document,
    ResearchQueryType,
    ResearchState,
    SearchResult,
)
from backend.services.laws_api_client import (
    LawsApiClient,
    SearchRequest,
    RecommendRequest,
    GraphRequest,
    PracticeArea,
    DocumentType,
    LawsApiException
)
from shared.core.llm import LLMConfig, LLMMessage, generate_completion, get_llm_client

# Configure logger
logger = logging.getLogger(__name__)


async def adaptive_classify_query(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Adaptive query classification with SELF-RAG principles.
    
    This function enhances basic query classification with self-reflective analysis
    to dynamically adjust retrieval strategies based on query complexity, following
    2025 SELF-RAG best practices for legal research applications.
    
    Args:
        state: Current research state with the user's question
        config: Runnable configuration
        
    Returns:
        Dict with status and next node to execute
    """
    # First perform basic classification
    basic_classification = await classify_query(state, config)
    
    if basic_classification.get("status") != "success":
        return basic_classification
    
    try:
        llm_client = get_llm_client()
        
        # Build reflection prompt for SELF-RAG analysis
        reflection_prompt = f"""You are a legal research AI analyzing query complexity for adaptive retrieval strategy.

Query: "{state.question}"
Basic Classification: {state.query_type}
Data Source: {state.data_source}
Jurisdiction: {state.jurisdiction}
Practice Areas: {list(state.practice_areas) if state.practice_areas else ['general']}
User Role: {state.user_context.role if hasattr(state.user_context, 'role') else 'unknown'}
Confidence: {state.search_metadata.get('classification', {}).get('confidence', 0.5)}

Analyze this legal query and determine the optimal retrieval strategy:

1. **Query Complexity Assessment**:
   - Simple: Single statute/case lookup, straightforward legal question
   - Moderate: Multi-factor analysis, comparing precedents, jurisdictional variations
   - Complex: Multi-jurisdiction analysis, evolving law areas, constitutional issues

2. **Retrieval Strategy Determination**:
   - "standard": Basic vector + graph retrieval (most queries)
   - "long_context": Need full document sections vs chunks (complex statutes, full case analysis)
   - "graph_multi_hop": Multi-step reasoning through legal relationships (precedent chains, doctrine evolution)

3. **Temporal Sensitivity**: Recent law changes, pending legislation, evolving case law

4. **Retrieval Necessity**: Some queries may not need document retrieval (basic legal definitions, procedural questions)

Return JSON with:
{{
  "strategy": "standard|long_context|graph_multi_hop",
  "retrieve": true|false,
  "depth": "shallow|deep", 
  "temporal": true|false,
  "confidence": 0.0-1.0,
  "reasoning": "brief explanation",
  "complexity": "simple|moderate|complex"
}}"""

        messages = [
            LLMMessage(role="system", content="You are an expert legal research strategist. Analyze queries and recommend optimal retrieval approaches for legal AI systems."),
            LLMMessage(role="user", content=reflection_prompt)
        ]
        
        # Get configurable model from agent config or default to gpt-4o
        model = "gpt-4o"
        try:
            from backend.agents.config import get_agent_config
            agent_config = get_agent_config("research_agent")
            if agent_config and agent_config.nodes and "adaptive_classify" in agent_config.nodes:
                node_config = agent_config.nodes["adaptive_classify"]
                if node_config.llm_config:
                    # Extract model name from provider/model format (e.g., "openai/gpt-4o" -> "gpt-4o")
                    model_name = node_config.llm_config.model_name
                    if "/" in model_name:
                        model = model_name.split("/")[-1]
                    else:
                        model = model_name
            elif agent_config and agent_config.llm_config:
                # Fallback to agent-level model config
                model_name = agent_config.llm_config.model_name
                if "/" in model_name:
                    model = model_name.split("/")[-1]
                else:
                    model = model_name
        except Exception as e:
            logger.warning(f"Could not load agent config for SELF-RAG model, using default: {e}")
        
        response = generate_completion(
            messages=messages,
            config=LLMConfig(
                model=model,
                temperature=0.1  # Low temperature for consistent classification
            )
        )
        
        # Parse SELF-RAG reflection results
        self_rag_params = json.loads(response.content)
        
        # Store adaptive parameters in state
        state.search_metadata["self_rag_params"] = self_rag_params
        
        # Apply adaptive adjustments based on reflection
        strategy = self_rag_params.get("strategy", "standard")
        
        # Adjust retrieval parameters based on strategy
        if strategy == "long_context":
            state.search_metadata["chunk_strategy"] = "full_sections"
            state.search_metadata["context_window"] = "large"
        elif strategy == "graph_multi_hop":
            state.search_metadata["graph_depth"] = 2
            state.search_metadata["multi_hop_reasoning"] = True
        
        # Handle temporal sensitivity
        if self_rag_params.get("temporal", False):
            state.search_metadata["temporal_filter"] = True
            state.search_metadata["recency_boost"] = True
        
        # Skip retrieval if not needed
        if not self_rag_params.get("retrieve", True):
            state.search_metadata["skip_retrieval"] = True
        
        # Legal safeguard: Flag uncertain classifications for human review
        confidence = self_rag_params.get("confidence", 0.5)
        complexity = self_rag_params.get("complexity", "moderate")
        
        if confidence < 0.7 or complexity == "complex":
            state.low_confidence_classification = True
            logger.warning(f"Low confidence classification detected: confidence={confidence:.3f}, complexity={complexity}")
        
        # Log adaptive strategy selection
        logger.info(f"SELF-RAG adaptive strategy: {strategy}, complexity: {complexity}, retrieve: {self_rag_params.get('retrieve', True)}")
        
        return basic_classification
        
    except json.JSONDecodeError as e:
        logger.warning(f"Failed to parse SELF-RAG reflection JSON: {e}")
        return basic_classification
    except Exception as e:
        logger.warning(f"SELF-RAG adaptive classification failed, using basic classification: {e}")
        return basic_classification


async def classify_query(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Classify the query to determine what data sources to search and the query type.

    This node analyzes the user's question to determine:
    1. The type of research query (legal_research, web_search, deep_research, non_legal)
    2. For legal research, the data source needs (public, private, hybrid)
    3. Whether the query references a specific case/client
    4. Whether the query contains sensitive information

    Args:
        state: Current research state with the user's question
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    llm_client = get_llm_client()

    system_prompt = """You are a legal research assistant specializing in personal injury law.
    Classify this query to determine what data sources to search and the appropriate research approach:

    Query: {query}
    User Role: {role}

    First, classify the query type:
    1. "legal_research" - Requires searching legal databases, case law, statutes, etc.
    2. "web_search" - Better answered through general web search (e.g., news, recent events, non-legal information)
    3. "deep_research" - Requires extensive research that may take longer to complete
    4. "non_legal" - Not related to legal research at all

    Then, for legal research queries, classify the data source needs:
    1. "public" - Only needs public legal information (laws, statutes, precedents)
    2. "private" - Only needs firm-specific information (client cases, internal documents)
    3. "hybrid" - Needs both public and private information

    Also determine if this query:
    - References a specific case/client
    - Contains sensitive information

    Return your classification as JSON with the following fields:
    - query_type: "legal_research", "web_search", "deep_research", or "non_legal"
    - data_source: "public", "private", "hybrid", or null (if not legal_research)
    - references_case: boolean
    - contains_sensitive_info: boolean
    - confidence: number between 0 and 1
    - reasoning: brief explanation of your classification
    """

    messages = [
        LLMMessage(role="system", content=system_prompt),
        LLMMessage(role="user", content=f"Query: {state.question}\nUser Role: {state.user_context['role'] if isinstance(state.user_context, dict) else state.user_context.role}")
    ]

    try:
        response = generate_completion(
            messages=messages,
            config=LLMConfig(
                model="gpt-4o",
                temperature=0.1
            )
        )

        result = json.loads(response.content)

        # Update state with classification results
        state.query_type = cast(ResearchQueryType, result.get("query_type"))
        state.data_source = cast(DataSourceType, result.get("data_source"))
        state.search_metadata["classification"] = {
            "references_case": result.get("references_case", False),
            "contains_sensitive_info": result.get("contains_sensitive_info", False),
            "confidence": result.get("confidence", 0.0),
            "reasoning": result.get("reasoning", "")
        }

        # For backward compatibility
        if state.data_source:
            state.inferred_mode = state.data_source

        # Log classification for monitoring
        logger.info(
            f"Query classified as type={state.query_type}, data_source={state.data_source}, "
            f"confidence={state.search_metadata['classification']['confidence']}"
        )

        return {"status": "success", "next": "route_query"}
    except Exception as e:
        logger.error(f"Error parsing classification response: {str(e)}")
        # Default to legal research with public data source if classification fails
        state.query_type = "legal_research"
        state.data_source = "public"
        state.inferred_mode = "public"
        return {"status": "error", "error": str(e), "next": "route_query"}


async def route_by_query_type(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Route the query based on its classified type.

    This node determines the next node to execute based on the query type:
    - legal_research: Route to query generation for legal database search
    - web_search: Route to web search
    - deep_research: Route to long research queue
    - non_legal: Route to web search (as a fallback)

    Args:
        state: Current research state with classification
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    query_type = state.query_type

    # Log routing decision
    logger.info(f"Routing query of type '{query_type}'")

    if query_type == "web_search":
        return {"status": "success", "next": "web_search"}
    elif query_type == "deep_research":
        return {"status": "success", "next": "long_research_queue"}
    elif query_type == "non_legal":
        # For non-legal queries, we still use web search but log this special case
        logger.info("Non-legal query detected, routing to web search")
        return {"status": "success", "next": "web_search"}
    else:
        # Default to legal research (includes when query_type is explicitly "legal_research")
        return {"status": "success", "next": "gpt_query_gen"}


async def gpt_query_gen(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Generate optimized search queries from the user's question.

    This node uses an LLM to reformulate the user's question into:
    1. 2-3 vector search queries (reformulations that preserve intent but use legal terminology)
    2. 1 keyword/boolean search query (for traditional search)

    The generated queries are optimized for the specified jurisdiction and practice areas.

    Args:
        state: Current research state with the user's question
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    llm_client = get_llm_client()

    system_prompt = """
    You are a legal research query optimizer. Convert the user's question into:
    1. 2-3 vector search queries (reformulations that preserve intent but use legal terminology)
    2. 1 keyword/boolean search query (for traditional search)

    Focus on jurisdiction: {jurisdiction}
    Practice areas: {practice_areas}

    Return your response as JSON with the following fields:
    - vector_queries: array of 2-3 string queries
    - keyword_query: string for boolean search
    """

    messages = [
        LLMMessage(
            role="system",
            content=system_prompt.format(
                jurisdiction=state.jurisdiction,
                practice_areas=", ".join(state.practice_areas) if state.practice_areas else "personal_injury"
            )
        ),
        LLMMessage(role="user", content=state.question)
    ]

    try:
        response = generate_completion(
            messages=messages,
            config=LLMConfig(
                model="gpt-4o",
                temperature=0.2
            )
        )

        result = json.loads(response.content)
        state.queries = result.get("vector_queries", [])
        state.keyword_query = result.get("keyword_query", "")

        # Log generated queries
        logger.info(f"Generated {len(state.queries)} vector queries and 1 keyword query")

        return {"status": "success", "next": "vector_search"}
    except Exception as e:
        logger.error(f"Error parsing query generation response: {str(e)}")
        # Create a simple fallback query if generation fails
        state.queries = [state.question]
        state.keyword_query = state.question
        return {"status": "error", "error": str(e), "next": "vector_search"}


async def vector_search(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Perform vector search using the laws-API service.

    This node searches for relevant legal documents using the laws-API.
    It respects the jurisdiction and practice areas specified in the state.

    Args:
        state: Current research state with generated queries
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    try:
        # Get auth token from config if available
        auth_token = getattr(config, 'auth_token', None) if config else None

        # Log search parameters
        logger.info(f"Laws-API search for queries: {state.queries}")
        logger.info(f"Jurisdiction: {state.jurisdiction}")
        logger.info(f"Practice areas: {state.practice_areas}")

        # Check if we're in a test environment with pre-populated legal_documents
        if not state.legal_documents and state.queries:
            all_results = []

            # Use laws-API client for search
            async with LawsApiClient() as client:
                for query in state.queries:
                    try:
                        # Convert practice areas to laws-API format
                        practice_area_filters = None
                        if state.practice_areas:
                            practice_area_filters = {
                                'practice_area': [
                                    PracticeArea.PERSONAL_INJURY.value
                                    if area.lower() in ['personal_injury', 'personal injury', 'pi']
                                    else area.lower()
                                    for area in state.practice_areas
                                ]
                            }

                        search_request = SearchRequest(
                            query=query,
                            jurisdiction=[state.jurisdiction] if state.jurisdiction else ['texas'],
                            limit=10,
                            filters=practice_area_filters
                        )

                        results = await client.search(search_request, auth_token)

                        # Convert laws-API results to Document format
                        for result in results:
                            doc = Document(
                                page_content=result.content,
                                metadata={
                                    "id": result.id,
                                    "title": result.title,
                                    "jurisdiction": result.jurisdiction,
                                    "document_type": result.document_type.value,
                                    "relevance_score": result.relevance_score,
                                    "citation": result.citation,
                                    "url": result.url,
                                    "highlights": result.highlights,
                                    "source": "laws_api"
                                }
                            )
                            all_results.append(doc)

                    except LawsApiException as e:
                        logger.warning(f"Laws-API search failed for query '{query}': {e}")
                        continue
                    except Exception as e:
                        logger.error(f"Unexpected error in laws-API search for query '{query}': {e}")
                        continue

            # Deduplicate results by ID
            unique_results = {}
            for doc in all_results:
                doc_id = doc.metadata.get("id")
                if doc_id and doc_id not in unique_results:
                    unique_results[doc_id] = doc
                elif not doc_id:
                    # For documents without ID, use content hash
                    content_hash = hash(doc.page_content[:100])
                    if content_hash not in unique_results:
                        unique_results[content_hash] = doc

            state.legal_documents = list(unique_results.values())

            # If no results from laws-API, fall back to mock results for testing
            if not state.legal_documents:
                logger.warning("No results from laws-API, using mock results for testing")
                mock_results = [
                    Document(
                        page_content=f"Mock legal document for query: {query}",
                        metadata={
                            "id": f"mock-doc-{i}",
                            "title": f"Mock Legal Document {i}",
                            "jurisdiction": state.jurisdiction,
                            "practice_area": list(state.practice_areas)[0] if state.practice_areas else "personal_injury",
                            "source": "mock"
                        }
                    )
                    for i, query in enumerate(state.queries[:3])  # Limit mock results
                ]
                state.legal_documents = mock_results

        # Log search results
        logger.info(f"Vector search found {len(state.legal_documents)} documents")

        # Log source breakdown
        sources = {}
        for doc in state.legal_documents:
            source = doc.metadata.get("source", "unknown")
            sources[source] = sources.get(source, 0) + 1
        logger.info(f"Document sources: {sources}")

        return {"status": "success", "next": "graph_expand"}
    except Exception as e:
        logger.error(f"Error in vector search: {str(e)}")
        return {"status": "error", "error": str(e), "next": "error"}


async def enhanced_graph_expand(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Enhanced graph expansion with dynamic entity summarization using GraphRAG techniques.
    
    This node performs basic graph traversal followed by LLM-powered entity summarization
    to extract legal insights from citation relationships. Follows 2025 GraphRAG best practices
    for Neo4j-based legal knowledge graphs with runtime query augmentation.
    
    Args:
        state: Current research state with initial search results
        config: Runnable configuration
        
    Returns:
        Dict with status and next node to execute
    """
    # First perform basic graph expansion
    basic_result = await graph_expand(state, config)
    
    if basic_result.get("status") != "success":
        return basic_result
    
    try:
        # Get configurable model from agent config or default to gpt-4o
        model = "gpt-4o"
        try:
            from backend.agents.config import get_agent_config
            agent_config = get_agent_config("research_agent")
            if agent_config and agent_config.nodes and "graph_expansion" in agent_config.nodes:
                node_config = agent_config.nodes["graph_expansion"]
                if node_config.llm_config:
                    model_name = node_config.llm_config.model_name
                    model = model_name.split("/")[-1] if "/" in model_name else model_name
            elif agent_config and agent_config.llm_config:
                model_name = agent_config.llm_config.model_name
                model = model_name.split("/")[-1] if "/" in model_name else model_name
        except Exception as e:
            logger.warning(f"Could not load agent config for graph expansion model, using default: {e}")
        
        # Focus on recently expanded documents from graph traversal (last 5 for efficiency)
        graph_docs = [
            doc for doc in state.legal_documents 
            if doc.metadata.get("source") == "laws_api_graph"
        ][-5:]  # Limit to last 5 for performance
        
        if not graph_docs:
            logger.info("No graph-sourced documents to enhance, skipping entity summarization")
            return basic_result
        
        enhanced_count = 0
        high_uncertainty_count = 0
        
        # Process each graph-sourced document for entity summarization
        for doc in graph_docs:
            try:
                # Build legal-focused summarization prompt
                summarization_prompt = f"""You are a legal research AI analyzing a legal document from a knowledge graph traversal.

Document Title: {doc.metadata.get('title', 'Unknown')}
Document Type: {doc.metadata.get('document_type', 'Unknown')}
Jurisdiction: {doc.metadata.get('jurisdiction', 'Unknown')}
Relationship: {doc.metadata.get('relationship_type', 'Unknown')} (related to source document)
Content Preview: {doc.page_content[:1000]}...

Extract and summarize key legal information focusing on:
1. **Legal Entities**: Parties, courts, judges, attorneys involved
2. **Legal Principles**: Core holdings, doctrines, precedents established  
3. **Jurisdictional Context**: Applicable law, venue, procedural rules
4. **Citation Relationships**: How this relates to other cases/statutes in precedent chains
5. **Temporal Insights**: Legal trends, evolving doctrine, recent developments

Provide your analysis as 2-3 sentences followed by structured JSON:

Summary: [2-3 sentence summary focusing on legal significance and precedential value]

JSON:
{{
  "entities": ["party1", "court", "judge", "attorney", ...],
  "relationships": ["cites", "distinguishes", "overrules", "follows", ...],
  "legal_principles": ["doctrine1", "holding1", "principle1", ...],
  "jurisdictional_scope": "state/federal/multi-jurisdiction",
  "summary": "Concise summary of legal significance",
  "uncertainty": 0.0-1.0,
  "precedential_weight": "high/medium/low",
  "temporal_relevance": "current/dated/evolving"
}}

Rate uncertainty (0.0-1.0) based on:
- Clarity of legal holdings (unclear holdings = higher uncertainty)
- Completeness of citation context (missing context = higher uncertainty)  
- Jurisdictional ambiguity (unclear jurisdiction = higher uncertainty)
- Document quality/completeness (truncated content = higher uncertainty)"""

                messages = [
                    LLMMessage(role="system", content="You are an expert legal research analyst specializing in case law analysis and legal knowledge graph interpretation. Focus on extracting actionable legal insights."),
                    LLMMessage(role="user", content=summarization_prompt)
                ]
                
                response = generate_completion(
                    messages=messages,
                    config=LLMConfig(
                        model=model,
                        temperature=0.1  # Low temperature for consistent legal analysis
                    )
                )
                
                # Parse the response to extract summary and JSON
                response_text = response.content.strip()
                
                # Try to extract JSON from the response
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                
                if json_start != -1 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    summary_text = response_text[:json_start].replace("Summary:", "").strip()
                    
                    try:
                        entity_analysis = json.loads(json_text)
                        
                        # Enrich document metadata with entity summary
                        doc.metadata["entity_summary"] = summary_text
                        doc.metadata["entity_analysis"] = entity_analysis
                        doc.metadata["graph_enhanced"] = True
                        doc.metadata["enhancement_model"] = model
                        
                        # Check for high uncertainty requiring human review
                        uncertainty = float(entity_analysis.get("uncertainty", 0.5))
                        if uncertainty > 0.5:
                            high_uncertainty_count += 1
                            doc.metadata["high_uncertainty"] = True
                        
                        enhanced_count += 1
                        
                    except (json.JSONDecodeError, ValueError) as e:
                        logger.warning(f"Could not parse entity analysis JSON for doc {doc.metadata.get('id')}: {e}")
                        # Still add basic enhancement
                        doc.metadata["entity_summary"] = summary_text if 'summary_text' in locals() else "Entity analysis available"
                        doc.metadata["graph_enhanced"] = True
                        doc.metadata["parse_error"] = True
                        enhanced_count += 1
                else:
                    logger.warning(f"Could not extract structured analysis for doc {doc.metadata.get('id')}")
                    # Add basic enhancement with just the response
                    doc.metadata["entity_summary"] = response_text[:200] + "..." if len(response_text) > 200 else response_text
                    doc.metadata["graph_enhanced"] = True
                    doc.metadata["analysis_partial"] = True
                    enhanced_count += 1
                    
            except Exception as e:
                logger.warning(f"Entity summarization failed for doc {doc.metadata.get('id')}: {e}")
                continue
        
        # Set confidence flag if significant uncertainty detected
        if high_uncertainty_count > 0:
            state.low_confidence_graph = True
            logger.warning(f"High uncertainty detected in {high_uncertainty_count}/{enhanced_count} graph expansions")
        
        # Community Detection Phase (Optional)
        # Enable based on tenant configuration to avoid overhead on simple queries
        enable_communities = _should_enable_community_detection(state)
        if enable_communities:
            await _perform_community_detection(state, model, graph_docs)
        
        # Log enhancement completion
        logger.info(f"Enhanced {enhanced_count} graph documents with entity summarization using {model}")
        if enhanced_count > 0:
            logger.info(f"Graph enhancement: {high_uncertainty_count} docs flagged for uncertainty review")
        
        return basic_result
        
    except Exception as e:
        logger.warning(f"Entity summarization enhancement failed, continuing with basic graph expansion: {e}")
        return basic_result


def _should_enable_community_detection(state: ResearchState) -> bool:
    """
    Determine if community detection should be enabled based on tenant configuration.
    
    Checks tenant settings, user subscription tier, and query complexity to decide
    whether to run expensive community detection and trend analysis.
    
    Args:
        state: Current research state with user context and query info
        
    Returns:
        bool: True if community detection should be enabled
    """
    try:
        # Check explicit per-query override first (for testing/debugging)
        if "enable_communities" in state.search_metadata:
            return state.search_metadata["enable_communities"]
        
        # Get tenant settings for community detection
        user_context = state.user_context
        if hasattr(user_context, 'settings') and user_context.settings:
            tenant_settings = user_context.settings
            
            # Check tenant-level community detection setting
            if "enable_community_detection" in tenant_settings:
                return tenant_settings["enable_community_detection"]
        
        # Fallback: Check user role/subscription tier
        if hasattr(user_context, 'role'):
            user_role = user_context.role
            # Enable for partners and attorneys by default (they benefit most from trend analysis)
            if user_role in ["partner", "attorney"]:
                return True
            # Disable for staff/paralegal by default (to control costs)
            elif user_role in ["staff", "paralegal"]:
                return False
        
        # Default: Disable community detection to avoid overhead unless explicitly enabled
        # This ensures we don't surprise users with additional processing time/costs
        return False
        
    except Exception as e:
        logger.warning(f"Error checking community detection settings: {e}")
        # Conservative default: disable on error
        return False


async def _perform_community_detection(state: ResearchState, model: str, graph_docs: List[Document]) -> None:
    """
    Perform Neo4j GDS community detection and LLM summarization of clusters.
    
    This function uses Neo4j Graph Data Science library to detect communities
    in legal citation networks and summarize trends within clusters.
    
    Args:
        state: Current research state to store cluster results
        model: LLM model name for cluster summarization
        graph_docs: List of graph-sourced documents to analyze
    """
    if not NEO4J_AVAILABLE:
        logger.warning("Neo4j driver not available, skipping community detection")
        return
    
    try:
        # Get Neo4j connection details from environment variables
        neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
        neo4j_user = os.getenv("NEO4J_USER", "neo4j")
        neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
        
        if not all([neo4j_uri, neo4j_user, neo4j_password]):
            logger.warning("Neo4j credentials not configured, skipping community detection")
            return
        
        # Extract document IDs for community analysis
        doc_ids = [doc.metadata.get("id") for doc in graph_docs if doc.metadata.get("id")]
        
        if len(doc_ids) < 2:
            logger.info("Insufficient documents for community detection (need at least 2)")
            return
        
        # Connect to Neo4j and perform GDS community detection
        driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
        
        clusters = {}
        cluster_count = 0
        high_uncertainty_clusters = 0
        
        with driver.session() as session:
            # Prepare jurisdiction filter for query-relevant subgraphs
            jurisdiction_filter = ""
            if state.jurisdiction and state.jurisdiction != "general":
                jurisdiction_filter = f"AND n.jurisdiction = '{state.jurisdiction}'"
            
            # Build Cypher query for GDS Louvain community detection
            # Limit to 50 nodes for efficiency in small firm setups
            community_query = f"""
            MATCH (n:Case)-[r:CITES]->(m:Case)
            WHERE n.id IN {doc_ids} OR m.id IN {doc_ids}
            {jurisdiction_filter}
            WITH COLLECT(DISTINCT n) + COLLECT(DISTINCT m) as nodes
            UNWIND nodes[0..50] as node
            MATCH (node)-[rel:CITES]-(connected)
            WHERE connected IN nodes
            CALL gds.louvain.stream({{
                nodeQuery: 'MATCH (n) WHERE n IN $nodes RETURN id(n) as id',
                relationshipQuery: 'MATCH (n)-[r:CITES]-(m) WHERE n IN $nodes AND m IN $nodes RETURN id(n) as source, id(m) as target',
                parameters: {{nodes: nodes}}
            }})
            YIELD nodeId, communityId
            MATCH (n) WHERE id(n) = nodeId
            RETURN n.id as document_id, n.title as title, n.content as content, 
                   n.jurisdiction as jurisdiction, communityId
            ORDER BY communityId
            """
            
            try:
                # Execute community detection query
                result = session.run(community_query, nodes=doc_ids)
                
                # Group results by community
                for record in result:
                    community_id = record["communityId"]
                    doc_id = record["document_id"]
                    
                    if community_id not in clusters:
                        clusters[community_id] = []
                    
                    # Create document for this cluster member
                    cluster_doc = Document(
                        page_content=record["content"] or "",
                        metadata={
                            "id": doc_id,
                            "title": record["title"] or "",
                            "jurisdiction": record["jurisdiction"] or state.jurisdiction,
                            "community_id": community_id,
                            "source": "community_cluster"
                        }
                    )
                    clusters[community_id].append(cluster_doc)
                
            except Exception as e:
                # Fall back to simple clustering if GDS not available
                logger.warning(f"GDS Louvain failed, using simple clustering: {e}")
                
                # Simple clustering fallback: group by jurisdiction and document type
                for i, doc in enumerate(graph_docs):
                    community_id = f"simple_{doc.metadata.get('jurisdiction', 'unknown')}"
                    if community_id not in clusters:
                        clusters[community_id] = []
                    clusters[community_id].append(doc)
        
        driver.close()
        
        # Summarize clusters with >1 document using LLM
        for community_id, cluster_docs in clusters.items():
            if len(cluster_docs) <= 1:
                continue  # Skip single-document clusters
            
            try:
                # Build cluster summarization prompt
                doc_titles = [doc.metadata.get("title", "Unknown") for doc in cluster_docs]
                jurisdictions = list(set([doc.metadata.get("jurisdiction", "Unknown") for doc in cluster_docs]))
                sample_content = "\n".join([doc.page_content[:200] + "..." for doc in cluster_docs[:3]])
                
                cluster_prompt = f"""You are analyzing a community cluster of related legal documents discovered through graph analysis.

Cluster ID: {community_id}
Document Count: {len(cluster_docs)}
Jurisdictions: {', '.join(jurisdictions)}
Document Titles: {'; '.join(doc_titles[:5])}

Sample Content:
{sample_content}

Analyze this cluster to identify legal trends, patterns, and insights:

1. **Legal Trends**: What patterns emerge across these cases/statutes?
2. **Jurisdictional Patterns**: How do jurisdictions align or differ?
3. **Temporal Evolution**: Are there chronological patterns in legal doctrine?
4. **Precedential Relationships**: What citation patterns exist within this cluster?
5. **Practical Insights**: What should attorneys know about this trend?

Focus especially on trends like:
- "This group of Texas non-compete cases favors employees post-2024"
- "These contract disputes show increasing judicial skepticism of arbitration clauses"
- "This cluster reveals a circuit split on digital privacy rights"

Provide your analysis as 2-3 sentences followed by structured JSON:

Summary: [2-3 sentence summary of key trends and insights in this legal cluster]

JSON:
{{
  "trend_summary": "Core legal trend identified in this cluster",
  "jurisdictional_scope": ["texas", "federal", ...],
  "temporal_pattern": "emerging/established/declining/evolving",
  "precedential_strength": "strong/moderate/weak",
  "attorney_insights": ["practical insight 1", "practical insight 2"],
  "uncertainty": 0.0-1.0,
  "cluster_quality": "high/medium/low"
}}

Rate uncertainty (0.0-1.0) based on:
- Clarity of trends (unclear patterns = higher uncertainty)
- Document coherence (inconsistent themes = higher uncertainty)
- Sample size adequacy (too few documents = higher uncertainty)
- Jurisdictional consistency (conflicting jurisdictions = higher uncertainty)"""

                messages = [
                    LLMMessage(role="system", content="You are an expert legal trend analyst specializing in pattern recognition across legal document clusters. Focus on identifying actionable insights for practicing attorneys."),
                    LLMMessage(role="user", content=cluster_prompt)
                ]
                
                response = generate_completion(
                    messages=messages,
                    config=LLMConfig(
                        model=model,
                        temperature=0.1  # Low temperature for consistent analysis
                    )
                )
                
                # Parse cluster analysis response
                response_text = response.content.strip()
                
                # Extract JSON from response
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                
                if json_start != -1 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    summary_text = response_text[:json_start].replace("Summary:", "").strip()
                    
                    try:
                        cluster_analysis = json.loads(json_text)
                        
                        # Store cluster summary in state metadata
                        state.search_metadata[f"cluster_{community_id}_summary"] = summary_text
                        state.search_metadata[f"cluster_{community_id}_analysis"] = cluster_analysis
                        state.search_metadata[f"cluster_{community_id}_doc_count"] = len(cluster_docs)
                        
                        # Check for high uncertainty requiring human review
                        uncertainty = float(cluster_analysis.get("uncertainty", 0.5))
                        if uncertainty > 0.5:
                            high_uncertainty_clusters += 1
                            state.search_metadata[f"cluster_{community_id}_high_uncertainty"] = True
                        
                        cluster_count += 1
                        
                    except (json.JSONDecodeError, ValueError) as e:
                        logger.warning(f"Could not parse cluster analysis JSON for community {community_id}: {e}")
                        # Store basic summary
                        state.search_metadata[f"cluster_{community_id}_summary"] = summary_text if 'summary_text' in locals() else "Cluster analysis available"
                        state.search_metadata[f"cluster_{community_id}_parse_error"] = True
                        cluster_count += 1
                else:
                    logger.warning(f"Could not extract structured analysis for community {community_id}")
                    # Store basic summary
                    state.search_metadata[f"cluster_{community_id}_summary"] = response_text[:200] + "..." if len(response_text) > 200 else response_text
                    state.search_metadata[f"cluster_{community_id}_partial"] = True
                    cluster_count += 1
                    
            except Exception as e:
                logger.warning(f"Cluster summarization failed for community {community_id}: {e}")
                continue
        
        # Update state with cluster results
        state.graph_clusters = cluster_count
        
        # Set confidence flag if significant uncertainty detected
        if high_uncertainty_clusters > 0:
            state.low_confidence_clusters = True
            logger.warning(f"High uncertainty detected in {high_uncertainty_clusters}/{cluster_count} community clusters")
        
        # Log community detection completion
        logger.info(f"Community detection completed: {len(clusters)} communities found, {cluster_count} summarized")
        if cluster_count > 0:
            logger.info(f"Community insights: {high_uncertainty_clusters} clusters flagged for uncertainty review")
        
    except Exception as e:
        logger.warning(f"Community detection failed, continuing without clusters: {e}")
        return


async def graph_expand(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Expand search results using laws-API graph relationships.

    This node finds related documents through citation relationships in the legal knowledge graph.
    It enriches the search results with documents that cite or are cited by the initial results.

    Args:
        state: Current research state with initial search results
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    # Skip if no documents found in initial search
    if not state.legal_documents:
        logger.info("No documents to expand, skipping graph expansion")
        return {"status": "success", "next": "rerank"}

    try:
        # Get auth token from config if available
        auth_token = getattr(config, 'auth_token', None) if config else None

        # Extract document IDs from initial search results that came from laws-API
        doc_ids = [
            doc.metadata.get("id")
            for doc in state.legal_documents
            if doc.metadata.get("id") and doc.metadata.get("source") == "laws_api"
        ]

        if not doc_ids:
            logger.info("No laws-API documents to expand, skipping graph expansion")
            return {"status": "success", "next": "rerank"}

        related_docs = []

        # Use laws-API client for graph expansion
        async with LawsApiClient() as client:
            for doc_id in doc_ids[:5]:  # Limit to first 5 documents to avoid too many API calls
                try:
                    graph_request = GraphRequest(
                        entity_id=doc_id,
                        depth=1,  # Only go one level deep
                        limit=3   # Limit related documents per source
                    )

                    graph_result = await client.graph_query(graph_request, auth_token)

                    # Extract related document IDs from graph nodes
                    related_doc_ids = [
                        node.id for node in graph_result.nodes
                        if node.id != doc_id  # Exclude the source document
                    ]

                    # For each related document, try to get its content via recommendation
                    if related_doc_ids:
                        recommend_request = RecommendRequest(
                            document_id=doc_id,
                            jurisdiction=[state.jurisdiction] if state.jurisdiction else ['texas'],
                            limit=3
                        )

                        recommendations = await client.recommend(recommend_request, auth_token)

                        # Convert recommendations to Document format
                        for rec in recommendations:
                            related_doc = Document(
                                page_content=rec.content,
                                metadata={
                                    "id": rec.id,
                                    "title": rec.title,
                                    "jurisdiction": rec.jurisdiction,
                                    "document_type": rec.document_type.value,
                                    "similarity_score": rec.similarity_score,
                                    "relationship_type": rec.relationship_type.value,
                                    "related_to": doc_id,
                                    "source": "laws_api_graph"
                                }
                            )
                            related_docs.append(related_doc)

                except LawsApiException as e:
                    logger.warning(f"Laws-API graph expansion failed for doc {doc_id}: {e}")
                    continue
                except Exception as e:
                    logger.error(f"Unexpected error in graph expansion for doc {doc_id}: {e}")
                    continue

        # Merge and deduplicate
        all_docs = state.legal_documents.copy()
        existing_ids = {doc.metadata.get("id") for doc in all_docs if doc.metadata.get("id")}

        for doc in related_docs:
            doc_id = doc.metadata.get("id")
            if doc_id and doc_id not in existing_ids:
                all_docs.append(doc)
                existing_ids.add(doc_id)

        state.legal_documents = all_docs

        # Log graph expansion results
        logger.info(f"Graph expansion found {len(related_docs)} related documents")
        logger.info(f"Total unique documents after expansion: {len(state.legal_documents)}")

        # Log source breakdown after expansion
        sources = {}
        for doc in state.legal_documents:
            source = doc.metadata.get("source", "unknown")
            sources[source] = sources.get(source, 0) + 1
        logger.info(f"Document sources after expansion: {sources}")

        return {"status": "success", "next": "rerank"}
    except Exception as e:
        logger.error(f"Error in graph expansion: {str(e)}")
        # Continue to reranking even if graph expansion fails
        return {"status": "error", "error": str(e), "next": "rerank"}


async def rerank(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Rerank documents using Voyage AI's reranker models.
    
    This node reranks the retrieved documents based on their relevance to the original question.
    It uses Voyage's reranking model for high-quality legal document relevance scoring.
    
    Args:
        state: Current research state with retrieved documents
        config: Runnable configuration
        
    Returns:
        Dict with status and next node to execute
    """
    # Skip if no documents to rerank
    if not state.legal_documents:
        logger.info("No documents to rerank, skipping reranking")
        return {"status": "success", "next": "collect_citations"}
    
    try:
        # Initialize Voyage client
        voyage_api_key = os.getenv("VOYAGE_API_KEY")
        if not voyage_api_key:
            logger.error("VOYAGE_API_KEY not found, falling back to mock reranking")
            raise ValueError("Voyage API key not configured")
        
        vo = voyageai.Client(api_key=voyage_api_key)
        
        # Prepare documents for reranking
        documents = [doc.page_content for doc in state.legal_documents]
        
        # Determine model based on config or use default
        model = getattr(config, 'rerank_model', 'rerank-2-lite') if config else 'rerank-2-lite'
        top_k = min(len(documents), 10)
        
        # Call Voyage rerank API
        rerank_result = vo.rerank(
            query=state.question,
            documents=documents,
            model=model,
            top_k=top_k,
            truncation=True  # Handle long legal documents
        )
        
        # Reorder documents based on relevance scores
        reranked_docs = []
        rerank_scores = []
        
        for result in rerank_result.results:
            original_index = result.index
            relevance_score = result.relevance_score
            
            reranked_docs.append(state.legal_documents[original_index])
            rerank_scores.append(relevance_score)
        
        # Update state with reranked documents and scores
        state.legal_documents = reranked_docs
        state.rerank_scores = rerank_scores
        
        # Legal safeguard: Check for low confidence results
        top_score = rerank_scores[0] if rerank_scores else 0.0
        if top_score < 0.5:
            state.low_confidence_rerank = True
            logger.warning(f"Low confidence reranking detected: top score {top_score:.3f}")
        
        # Log reranking results
        logger.info(f"Reranked {len(reranked_docs)} documents using {model}, top score: {top_score:.3f}")
        
        return {"status": "success", "next": "collect_citations"}
        
    except Exception as e:
        logger.error(f"Error in Voyage reranking: {str(e)}")
        # Graceful fallback: Continue without reranking but mark as error
        return {"status": "error", "error": str(e), "next": "collect_citations"}


async def web_search(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Perform web search as an alternative research path.

    This node uses Perplexity API or a similar service to search the web for information
    relevant to the user's question. It's used as an alternative to legal database search
    when the query is better suited for general web search.

    Args:
        state: Current research state with the user's question
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    try:
        # In a real implementation, this would use Perplexity API or a similar service
        # For now, we'll mock the web search results

        # Mock web search results
        mock_results = [
            {
                "id": f"web-{i+1}",
                "title": f"Web Result {i+1} for '{state.question[:30]}...'",
                "url": f"https://example.com/result-{i+1}",
                "snippet": f"This is a mock web search result {i+1} for the query: {state.question[:50]}..."
            }
            for i in range(5)
        ]

        # Store web search results in state
        state.web_search_results = mock_results

        # Log web search results
        logger.info(f"Web search found {len(state.web_search_results)} results")

        return {"status": "success", "next": "web_citation_mapper"}
    except Exception as e:
        logger.error(f"Error in web search: {str(e)}")
        return {"status": "error", "error": str(e), "next": "error"}


async def web_citation_mapper(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Process web search results into citation format.

    This node converts web search results into a standardized citation format
    that can be used in the final answer.

    Args:
        state: Current research state with web search results
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    try:
        web_citations = []

        for i, result in enumerate(state.web_search_results):
            citation = {
                "id": result.get("id", f"web-{i+1}"),
                "title": result.get("title", f"Web Source {i+1}"),
                "url": result.get("url", ""),
                "source_type": "web",
                "jurisdiction": state.jurisdiction,
                "citation_text": result.get("snippet", "")
            }
            web_citations.append(citation)

        # Store web citations in state
        state.citations = web_citations

        # Create citation map for numbered references
        for i, citation in enumerate(web_citations):
            state.citation_map[i + 1] = citation

        # Log citation mapping
        logger.info(f"Mapped {len(state.citations)} web citations")

        return {"status": "success", "next": "llm_answer"}
    except Exception as e:
        logger.error(f"Error in web citation mapping: {str(e)}")
        return {"status": "error", "error": str(e), "next": "error"}


async def collect_citations(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Collect and format citations from search results.

    This node processes legal documents and web search results (if any)
    into a standardized citation format that can be used in the final answer.

    Args:
        state: Current research state with search results
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    try:
        citations = []
        citation_map = {}

        # Process legal documents
        for i, doc in enumerate(state.legal_documents[:10]):
            citation_num = i + 1
            citation = {
                "id": doc.metadata.get("id", f"doc-{citation_num}"),
                "title": doc.metadata.get("title", f"Document {citation_num}"),
                "url": doc.metadata.get("url", ""),
                "source_type": "legal",
                "jurisdiction": doc.metadata.get("jurisdiction", state.jurisdiction),
                "citation_text": doc.metadata.get("citation_text", "")
            }
            citations.append(citation)
            citation_map[citation_num] = citation

        # Store citations in state
        state.citations = citations
        state.citation_map = citation_map

        # Log citation collection
        logger.info(f"Collected {len(state.citations)} citations")

        return {"status": "success", "next": "llm_answer"}
    except Exception as e:
        logger.error(f"Error in citation collection: {str(e)}")
        return {"status": "error", "error": str(e), "next": "llm_answer"}


async def llm_answer(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Generate answer with numbered citations.

    This node uses an LLM to generate a comprehensive answer to the user's question
    based on the retrieved documents. It includes numbered citations that correspond
    to the documents in the citation map.

    Args:
        state: Current research state with search results and citations
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    llm_client = get_llm_client()

    # Format documents with citation numbers
    formatted_docs = []

    # Process legal documents
    for i, doc in enumerate(state.legal_documents[:10]):
        citation_num = i + 1
        formatted_docs.append(f"Document {citation_num}: {doc.page_content}")

    # Process web search results if any
    web_start_idx = len(state.legal_documents[:10]) + 1
    for i, result in enumerate(state.web_search_results):
        citation_num = web_start_idx + i
        formatted_docs.append(f"Document {citation_num}: {result.get('snippet', '')}")

    # If no documents were found, inform the user
    if not formatted_docs:
        state.answer = "I couldn't find any relevant information to answer your question. Please try rephrasing your question or providing more context."
        return {"status": "success", "next": "exit_guard"}

    system_prompt = """
    You are a legal research assistant providing answers based on the documents provided.

    When referencing information from the documents, use numbered citations like this: $1$, $2$, etc.
    The numbers should correspond to the document numbers provided.

    Be concise, accurate, and only reference information that is directly supported by the documents.
    If the documents don't contain enough information to answer the question, say so clearly.

    Jurisdiction: {jurisdiction}
    Practice areas: {practice_areas}
    """

    messages = [
        LLMMessage(
            role="system",
            content=system_prompt.format(
                jurisdiction=state.jurisdiction,
                practice_areas=", ".join(state.practice_areas) if state.practice_areas else "personal_injury"
            )
        ),
        LLMMessage(
            role="user",
            content=f"Question: {state.question}\n\nDocuments:\n" + "\n\n".join(formatted_docs)
        )
    ]

    try:
        # Generate answer
        response = generate_completion(
            messages=messages,
            config=LLMConfig(
                model="gpt-4o",
                temperature=0.3
            )
        )

        # Store answer in state
        state.answer = response.content

        # Log answer generation
        logger.info(f"Generated answer of length {len(state.answer)}")

        return {"status": "success", "next": "exit_guard"}
    except Exception as e:
        logger.error(f"Error in answer generation: {str(e)}")
        # Provide a fallback answer if generation fails
        state.answer = "I encountered an error while generating an answer. Please try again later."
        return {"status": "error", "error": str(e), "next": "exit_guard"}


async def exit_guard(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Ensure answer is legally relevant and redact sensitive information.

    This node validates the generated answer to ensure it's legally relevant,
    properly formatted with citations, and doesn't contain sensitive information.

    Args:
        state: Current research state with generated answer
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    # Skip validation if no answer was generated
    if not state.answer:
        return {"status": "success", "next": "FINISH"}

    try:
        # In a real implementation, this would use a smaller, faster model for validation
        # For now, we'll just pass through the answer

        # Log exit guard completion
        logger.info("Exit guard validated answer")

        # Create search result object for the response
        result = SearchResult(
            query_type=state.query_type or "legal_research",
            data_source=state.data_source,
            answer=state.answer,
            citations=[Citation(**citation) for citation in state.citations],
            summary=f"Research results for: {state.question[:50]}...",
            recommendations=[],
            search_metadata=state.search_metadata
        )

        # Store result in state metadata
        state.search_metadata["result"] = result.model_dump()

        return {"status": "success", "next": "FINISH"}
    except Exception as e:
        logger.error(f"Error in exit guard: {str(e)}")
        return {"status": "error", "error": str(e), "next": "FINISH"}


async def long_research_queue(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Queue a deeper research task for asynchronous processing.

    This node handles queries that require extensive research by queuing them
    for asynchronous processing. It generates a response indicating that the
    research is in progress and will be completed later.

    Args:
        state: Current research state with the user's question
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    try:
        # In a real implementation, this would create a Celery task for deeper research
        # For now, we'll just generate a mock task ID and response

        # Generate mock task ID
        task_id = f"task-{state.query_id}"

        # Update state with task information
        state.search_metadata["long_research_task_id"] = task_id

        # Generate a response indicating the research is queued
        state.answer = f"""
        I've queued a deeper research task for your question. This will involve a more comprehensive analysis of relevant legal sources and may take some time to complete.

        You'll receive a notification when the research is ready. The task ID is: {task_id}

        In the meantime, here's a preliminary answer based on the information I have now:

        Your question involves complex legal research that requires in-depth analysis. I'll be examining relevant statutes, case law, and legal precedents to provide you with a comprehensive answer.
        """

        # Log task queuing
        logger.info(f"Queued long research task with ID {task_id}")

        return {"status": "success", "next": "FINISH"}
    except Exception as e:
        logger.error(f"Error in long research queue: {str(e)}")
        # Provide a fallback answer if queuing fails
        state.answer = "I encountered an error while queuing your research request. Please try again later."
        return {"status": "error", "error": str(e), "next": "FINISH"}


async def handle_error(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Handle errors in the research process.

    This node is called when an error occurs in any of the other nodes.
    It logs the error and generates an appropriate error message for the user.

    Args:
        state: Current research state with error information
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    error_message = state.search_metadata.get("error", "An unknown error occurred")
    logger.error(f"Research process error: {error_message}")

    # Generate an appropriate error message for the user
    state.answer = """
    I encountered an error while processing your research request.

    This could be due to:
    - Temporary service issues
    - Complex query that needs reformulation
    - Missing or unavailable data sources

    Please try again later or rephrase your question to be more specific.
    """


async def process_case_documents(state: ResearchState, config: RunnableConfig) -> Dict[str, Any]:
    """
    Process case-specific documents with multimodal analysis.

    This node retrieves and processes case documents using the unified multimodal
    document processor. It supports both legal and medical documents with domain-specific
    analysis to enhance research results with case-specific insights.

    Args:
        state: Current research state with case_id and user context
        config: Runnable configuration

    Returns:
        Dict with status and next node to execute
    """
    try:
        # Skip if no case_id provided
        if not state.case_id:
            logger.info("No case_id provided, skipping case document processing")
            return {"status": "success", "next": "rerank"}

        # Skip if case documents already processed
        if state.case_documents:
            logger.info("Case documents already processed, skipping")
            return {"status": "success", "next": "rerank"}

        logger.info(f"Processing case documents for case_id: {state.case_id}")

        # Get tenant_id from user context
        tenant_id = state.user_context.tenant_id
        if not tenant_id:
            logger.warning("No tenant_id in user context, skipping case document processing")
            return {"status": "success", "next": "rerank"}

        # TODO: Implement database query to get case documents
        # This would typically query the tenants.case_documents table
        # For now, we'll use a placeholder implementation

        # Mock case documents for demonstration
        # In production, this would query the database for actual documents
        case_document_paths = []

        # Process each document with multimodal analysis
        processed_documents = []

        for doc_path in case_document_paths:
            try:
                # Determine domain based on document type or content
                # This could be enhanced with ML classification
                domain = "legal"  # Default to legal

                # Check if document appears to be medical based on path or metadata
                if any(keyword in doc_path.lower() for keyword in ['medical', 'health', 'diagnosis', 'treatment']):
                    domain = "medical"

                # Process document with multimodal processor
                documents = await process_multimodal_document(
                    document_path=doc_path,
                    document_type="pdf",
                    domain=domain
                )

                # Add case-specific metadata
                for doc in documents:
                    doc.metadata.update({
                        "case_id": state.case_id,
                        "tenant_id": tenant_id,
                        "source": "case_document",
                        "processed_by": "multimodal_processor"
                    })

                processed_documents.extend(documents)

            except Exception as e:
                logger.error(f"Error processing case document {doc_path}: {str(e)}")
                continue

        # Update state with processed case documents
        state.case_documents = processed_documents

        # Log processing results
        logger.info(f"Processed {len(processed_documents)} case document elements")

        # Log confidence and review flags
        low_confidence_count = sum(1 for doc in processed_documents if doc.metadata.get("low_confidence", False))
        requires_review_count = sum(1 for doc in processed_documents if doc.metadata.get("requires_review", False))

        if low_confidence_count > 0:
            logger.warning(f"{low_confidence_count} case documents flagged for low confidence")
        if requires_review_count > 0:
            logger.info(f"{requires_review_count} case documents require human review")

        # Update search metadata
        state.search_metadata.update({
            "case_documents_processed": len(processed_documents),
            "case_documents_low_confidence": low_confidence_count,
            "case_documents_require_review": requires_review_count
        })

        return {"status": "success", "next": "rerank"}

    except Exception as e:
        logger.error(f"Error in case document processing: {str(e)}")
        return {"status": "error", "error": str(e), "next": "error"}

    return {"status": "error", "next": "FINISH"}
