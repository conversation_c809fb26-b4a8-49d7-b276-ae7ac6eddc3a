"""
AiLex Unified Multimodal Document Processor

This module provides a production-ready unified multimodal document processor that combines
Unstructured OSS for parsing with domain-specific analysis: Gemini 2.5 Pro API for legal docs
and MedGemma (via Hugging Face or Vertex AI) for medical docs.

This enhances the Research Agent in AiLex by automating comprehensive document review across
domains to create massive value for solo attorneys and small firms.

Key Features:
- Unified processing for legal and medical multimodal documents
- Unstructured OSS for high-quality document parsing
- Domain-specific AI models: Gemini 2.5 Pro for legal, MedGemma for medical
- Supports PDFs with text, tables, and images
- Confidence scoring and human review flagging
- Integration with Research Agent's LangGraph workflow
"""

import asyncio
import logging
import os
from typing import Any, Dict, List, Optional, Union

from langchain_core.documents import Document
import google.generativeai as genai

# Try to import Part, fallback if not available
try:
    from google.generativeai.types import Part
except ImportError:
    try:
        from google.generativeai import Part
    except ImportError:
        Part = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Optional imports for full functionality
try:
    from unstructured.partition.pdf import partition_pdf
    UNSTRUCTURED_AVAILABLE = True
except ImportError:
    UNSTRUCTURED_AVAILABLE = False
    partition_pdf = None

try:
    from transformers import pipeline
    import torch
    TRANSFORMERS_AVAILABLE = True
except (ImportError, RuntimeError, ValueError) as e:
    logger.warning(f"Transformers not available: {str(e)}")
    TRANSFORMERS_AVAILABLE = False
    pipeline = None
    torch = None


class MultimodalDocumentProcessor:
    """
    Unified multimodal document processor for legal and medical documents.
    
    Combines Unstructured OSS parsing with domain-specific AI analysis:
    - Legal documents: Gemini 2.5 Pro for general reliability
    - Medical documents: MedGemma 27B for specialized medical accuracy
    """
    
    def __init__(self):
        """Initialize the multimodal document processor."""
        self._setup_gemini()
        self._medgemma_pipeline = None
        self._vertex_client = None
        
    def _setup_gemini(self):
        """Setup Gemini API client."""
        api_key = os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
        if not api_key:
            logger.error("GOOGLE_API_KEY or GEMINI_API_KEY not found")
            raise ValueError("Gemini API key not configured")
        
        genai.configure(api_key=api_key)
        self.gemini_model = genai.GenerativeModel("gemini-2.0-flash-exp")
        logger.info("Gemini 2.0 Flash initialized successfully")
    
    def _get_medgemma_pipeline(self):
        """Lazy initialization of MedGemma pipeline via Hugging Face."""
        if self._medgemma_pipeline is None and TRANSFORMERS_AVAILABLE:
            try:
                hf_token = os.getenv("HF_TOKEN")
                if not hf_token:
                    logger.warning("HF_TOKEN not found, trying without authentication")

                # Initialize MedGemma pipeline
                self._medgemma_pipeline = pipeline(
                    "text-generation",
                    model="google/medgemma-27b",
                    torch_dtype=torch.float16,
                    device_map="auto",
                    token=hf_token,
                    max_new_tokens=512,
                    temperature=0.1,
                    do_sample=True
                )
                logger.info("MedGemma 27B pipeline initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize MedGemma pipeline: {str(e)}")
                # Fallback to Vertex AI if available
                self._setup_vertex_medgemma()
        elif not TRANSFORMERS_AVAILABLE:
            logger.warning("Transformers not available, falling back to Vertex AI")
            self._setup_vertex_medgemma()

        return self._medgemma_pipeline
    
    def _setup_vertex_medgemma(self):
        """Setup Vertex AI client for MedGemma as fallback."""
        try:
            from google.cloud import aiplatform
            
            project_id = os.getenv("GOOGLE_CLOUD_PROJECT", "new-texas-laws")
            aiplatform.init(project=project_id, location="us-central1")
            self._vertex_client = aiplatform
            logger.info("Vertex AI client initialized for MedGemma fallback")
        except Exception as e:
            logger.error(f"Failed to setup Vertex AI: {str(e)}")
            self._vertex_client = None

    async def process_multimodal_document(
        self, 
        document_path: str, 
        document_type: str = "pdf", 
        domain: str = "legal"
    ) -> List[Document]:
        """
        Process a multimodal document with domain-specific analysis.
        
        Args:
            document_path: Path to the document file
            document_type: Type of document (default: "pdf")
            domain: Processing domain ("legal" or "medical")
            
        Returns:
            List of enriched LangChain Documents with multimodal analysis
        """
        try:
            logger.info(f"Processing {domain} document: {document_path}")
            
            # Step 1: Parse document with Unstructured
            elements = await self._parse_with_unstructured(document_path)
            
            # Step 2: Process elements with domain-specific models
            documents = []
            for element in elements:
                try:
                    # Analyze element based on domain
                    if domain == "legal":
                        analysis = await self._analyze_legal_element(element)
                    elif domain == "medical":
                        analysis = await self._analyze_medical_element(element)
                    else:
                        logger.warning(f"Unknown domain '{domain}', defaulting to legal")
                        analysis = await self._analyze_legal_element(element)
                    
                    # Create enriched document
                    doc = self._create_enriched_document(element, analysis, domain)
                    documents.append(doc)
                    
                except Exception as e:
                    logger.error(f"Error processing element: {str(e)}")
                    # Create fallback document with text-only content
                    fallback_doc = self._create_fallback_document(element, domain, str(e))
                    documents.append(fallback_doc)
            
            logger.info(f"Successfully processed {len(documents)} document elements")
            return documents
            
        except Exception as e:
            logger.error(f"Error in multimodal document processing: {str(e)}")
            # Return empty list on complete failure
            return []

    async def _parse_with_unstructured(self, document_path: str) -> List[Any]:
        """Parse document using Unstructured with hi_res strategy."""
        if not UNSTRUCTURED_AVAILABLE:
            logger.warning("Unstructured not available, using fallback text extraction")
            return await self._fallback_text_extraction(document_path)

        try:
            # Use hi_res strategy for best quality parsing
            elements = partition_pdf(
                filename=document_path,
                strategy="hi_res",
                infer_table_structure=True,
                extract_images_in_pdf=True,
                extract_image_block_types=["Image", "Table"],
                chunking_strategy="by_title",
                max_characters=1000,
                combine_text_under_n_chars=100
            )

            logger.info(f"Unstructured parsed {len(elements)} elements")
            return elements

        except Exception as e:
            logger.error(f"Error parsing with Unstructured: {str(e)}")
            logger.info("Falling back to simple text extraction")
            return await self._fallback_text_extraction(document_path)

    async def _fallback_text_extraction(self, document_path: str) -> List[Any]:
        """Fallback text extraction when Unstructured is not available."""
        try:
            import fitz  # PyMuPDF

            class SimpleElement:
                def __init__(self, content, element_type="Text"):
                    self.content = content
                    self.element_type = element_type
                    self.metadata = {}

                def __str__(self):
                    return self.content

            doc = fitz.open(document_path)
            elements = []

            for page_num in range(len(doc)):
                page = doc[page_num]
                text = page.get_text()

                if text.strip():
                    # Split into chunks
                    chunks = [text[i:i+1000] for i in range(0, len(text), 800)]
                    for chunk in chunks:
                        if chunk.strip():
                            elements.append(SimpleElement(chunk.strip()))

            doc.close()
            logger.info(f"Fallback extraction parsed {len(elements)} text elements")
            return elements

        except Exception as e:
            logger.error(f"Error in fallback text extraction: {str(e)}")
            # Return a single empty element to prevent complete failure
            class EmptyElement:
                def __init__(self):
                    self.content = ""
                    self.element_type = "Text"
                    self.metadata = {}

                def __str__(self):
                    return ""

            return [EmptyElement()]

    async def _analyze_legal_element(self, element) -> Dict[str, Any]:
        """Analyze document element using Gemini for legal domain."""
        try:
            element_type = str(type(element).__name__)
            content = str(element)
            
            # Legal-specific prompt with Texas jurisdiction awareness
            prompt = f"""
            Analyze this legal {element_type}: {content}
            
            Summarize key clauses, risks, and legal implications with focus on Texas statutes and regulations.
            If this is a table, extract structured JSON data.
            If this contains images, describe legal relevance.
            
            Flag uncertainty >0.5 for human review.
            Provide confidence score (0.0-1.0).
            
            Format response as JSON:
            {{
                "summary": "Brief legal analysis",
                "key_points": ["point1", "point2"],
                "risks": ["risk1", "risk2"],
                "texas_relevance": "Texas-specific legal context",
                "structured_data": {{}},
                "confidence": 0.0-1.0,
                "requires_review": boolean
            }}
            """
            
            # Handle images/tables as Parts for Gemini
            if hasattr(element, 'image_path') and element.image_path:
                # Create Part for image
                with open(element.image_path, 'rb') as img_file:
                    image_part = Part.from_data(img_file.read(), mime_type="image/jpeg")
                response = await self.gemini_model.generate_content_async([prompt, image_part])
            else:
                response = await self.gemini_model.generate_content_async(prompt)
            
            # Parse JSON response
            import json
            try:
                analysis = json.loads(response.text)
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                analysis = {
                    "summary": response.text[:500],
                    "key_points": [],
                    "risks": [],
                    "texas_relevance": "",
                    "structured_data": {},
                    "confidence": 0.5,
                    "requires_review": True
                }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in legal analysis: {str(e)}")
            return {
                "summary": f"Analysis failed: {str(e)}",
                "key_points": [],
                "risks": [],
                "texas_relevance": "",
                "structured_data": {},
                "confidence": 0.0,
                "requires_review": True
            }

    async def _analyze_medical_element(self, element) -> Dict[str, Any]:
        """Analyze document element using MedGemma for medical domain."""
        try:
            element_type = str(type(element).__name__)
            content = str(element)
            
            # Medical-specific prompt with legal relevance for PI/med mal cases
            prompt = f"""
            Analyze this medical {element_type}: {content}
            
            Extract diagnoses, FHIR data, and clinical insights.
            Summarize medical findings and flag legal relevance for malpractice claims.
            Focus on causation links, standard of care deviations, and injury documentation.
            
            Flag uncertainty >0.5 for human review.
            Provide confidence score (0.0-1.0).
            
            Response format:
            {{
                "diagnoses": ["diagnosis1", "diagnosis2"],
                "clinical_summary": "Medical findings summary",
                "fhir_data": {{}},
                "legal_relevance": "Relevance for PI/med mal cases",
                "causation_links": ["link1", "link2"],
                "standard_of_care": "Assessment of care quality",
                "confidence": 0.0-1.0,
                "requires_review": boolean
            }}
            """
            
            # Try MedGemma pipeline first
            pipeline = self._get_medgemma_pipeline()
            if pipeline:
                response = pipeline(prompt, max_new_tokens=512, temperature=0.1)[0]['generated_text']
                
                # Extract JSON from response
                import json
                try:
                    # Look for JSON in response
                    json_start = response.find('{')
                    json_end = response.rfind('}') + 1
                    if json_start != -1 and json_end > json_start:
                        analysis = json.loads(response[json_start:json_end])
                    else:
                        raise json.JSONDecodeError("No JSON found", response, 0)
                except json.JSONDecodeError:
                    # Fallback structure
                    analysis = {
                        "diagnoses": [],
                        "clinical_summary": response[:500],
                        "fhir_data": {},
                        "legal_relevance": "Requires manual review",
                        "causation_links": [],
                        "standard_of_care": "Assessment needed",
                        "confidence": 0.5,
                        "requires_review": True
                    }
            else:
                # Fallback to Gemini with medical context
                logger.warning("MedGemma unavailable, falling back to Gemini with medical context")
                response = await self.gemini_model.generate_content_async(prompt)
                
                import json
                try:
                    analysis = json.loads(response.text)
                except json.JSONDecodeError:
                    analysis = {
                        "diagnoses": [],
                        "clinical_summary": response.text[:500],
                        "fhir_data": {},
                        "legal_relevance": "Gemini fallback analysis",
                        "causation_links": [],
                        "standard_of_care": "Requires specialist review",
                        "confidence": 0.4,
                        "requires_review": True
                    }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in medical analysis: {str(e)}")
            return {
                "diagnoses": [],
                "clinical_summary": f"Analysis failed: {str(e)}",
                "fhir_data": {},
                "legal_relevance": "Error in processing",
                "causation_links": [],
                "standard_of_care": "Manual review required",
                "confidence": 0.0,
                "requires_review": True
            }

    def _create_enriched_document(self, element, analysis: Dict[str, Any], domain: str) -> Document:
        """Create enriched LangChain Document with multimodal analysis."""
        try:
            # Extract basic element information
            element_type = str(type(element).__name__)
            content = str(element)

            # Create base metadata
            metadata = {
                "element_type": element_type,
                "domain": domain,
                "analysis": analysis,
                "confidence": analysis.get("confidence", 0.5),
                "requires_review": analysis.get("requires_review", True),
                "processing_model": "gemini-2.0-flash" if domain == "legal" else "medgemma-27b"
            }

            # Add domain-specific metadata
            if domain == "legal":
                metadata.update({
                    "legal_summary": analysis.get("summary", ""),
                    "key_points": analysis.get("key_points", []),
                    "risks": analysis.get("risks", []),
                    "texas_relevance": analysis.get("texas_relevance", ""),
                    "structured_data": analysis.get("structured_data", {})
                })
            elif domain == "medical":
                metadata.update({
                    "diagnoses": analysis.get("diagnoses", []),
                    "clinical_summary": analysis.get("clinical_summary", ""),
                    "fhir_data": analysis.get("fhir_data", {}),
                    "legal_relevance": analysis.get("legal_relevance", ""),
                    "causation_links": analysis.get("causation_links", []),
                    "standard_of_care": analysis.get("standard_of_care", "")
                })

            # Flag low confidence for human review
            if analysis.get("confidence", 0.5) < 0.5:
                metadata["low_confidence"] = True
                metadata["requires_review"] = True

            # Add element-specific metadata
            if hasattr(element, 'metadata'):
                metadata.update(element.metadata)

            return Document(
                page_content=content,
                metadata=metadata
            )

        except Exception as e:
            logger.error(f"Error creating enriched document: {str(e)}")
            return self._create_fallback_document(element, domain, str(e))

    def _create_fallback_document(self, element, domain: str, error: str) -> Document:
        """Create fallback document when processing fails."""
        element_type = str(type(element).__name__)
        content = str(element)

        metadata = {
            "element_type": element_type,
            "domain": domain,
            "processing_error": error,
            "confidence": 0.0,
            "requires_review": True,
            "low_confidence": True,
            "fallback": True
        }

        return Document(
            page_content=content,
            metadata=metadata
        )


# Convenience function for Research Agent integration
async def process_multimodal_document(
    document_path: str,
    document_type: str = "pdf",
    domain: str = "legal"
) -> List[Document]:
    """
    Convenience function to process multimodal documents.

    This function provides the main interface for the Research Agent to process
    multimodal documents with domain-specific analysis.

    Args:
        document_path: Path to the document file
        document_type: Type of document (default: "pdf")
        domain: Processing domain ("legal" or "medical", default: "legal")

    Returns:
        List of enriched LangChain Documents with multimodal analysis

    Example:
        >>> documents = await process_multimodal_document(
        ...     "/path/to/medical_report.pdf",
        ...     domain="medical"
        ... )
        >>> for doc in documents:
        ...     print(f"Confidence: {doc.metadata['confidence']}")
        ...     if doc.metadata.get('diagnoses'):
        ...         print(f"Diagnoses: {doc.metadata['diagnoses']}")
    """
    processor = MultimodalDocumentProcessor()
    return await processor.process_multimodal_document(document_path, document_type, domain)
