# =========================================================
# LangGraph FastAPI Dockerfile for Fly.io Deployment
# =========================================================
# This Dockerfile is designed for deploying a FastAPI application with
# LangGraph and CopilotKit integration to Fly.io.
#
# It uses a multi-stage build process to:
# 1. Create a build environment with all necessary tools
# 2. Install Python dependencies efficiently
# 3. Create a minimal runtime environment
# 4. Configure security and performance settings
#
# The resulting image is optimized for:
# - Security (runs as non-root user)
# - Size (minimal dependencies in final image)
# - Performance (optimized for Fly.io deployment)
# - Maintainability (clear structure and documentation)

# =========================================================
# BUILDER STAGE
# =========================================================
FROM python:3.11-slim AS builder

# Set working directory
WORKDIR /app

# Set environment variables to optimize Python
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies required for building Python packages
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    git \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create and activate virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies in layers to leverage Docker caching
# Core dependencies first
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir fastapi uvicorn pydantic python-dotenv

# Install LangGraph and CopilotKit dependencies
RUN pip install --no-cache-dir \
    copilotkit>=0.1.46 \
    langgraph>=0.2.50 \
    langchain>=0.3.25 \
    langchain-openai>=0.3.17 \
    voyageai>=0.1.6 \
    pinecone>=2.2.1

# Install remaining dependencies
RUN pip install --no-cache-dir -r requirements.txt

# =========================================================
# FINAL STAGE
# =========================================================
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/opt/venv/bin:$PATH" \
    PORT=8000

# Install runtime system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Copy application code
COPY src/ /app/src/
COPY backend/ /app/backend/
COPY shared/ /app/shared/
COPY setup.py /app/
COPY langgraph.json /app/
COPY start.sh /app/

# Install the package in development mode to set up Python paths
RUN pip install -e .

# Create a non-root user to run the application
RUN addgroup --system --gid 1001 appgroup && \
    adduser --system --uid 1001 --gid 1001 appuser && \
    chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Expose the port the app will run on
EXPOSE 8000

# Create a directory for logs
RUN mkdir -p /app/logs && chown -R appuser:appgroup /app/logs

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Make the startup script executable
RUN chmod +x /app/start.sh

# Command to run the application
# The startup script ensures environment variables are properly loaded
CMD ["/app/start.sh"]
